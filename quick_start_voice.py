#!/usr/bin/env python3
"""
QUICK START VOICE SYSTEM
Simple activation and demo of the optimized voice AI system
"""

import asyncio
import logging
import time
import sys
import subprocess
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("quick_start")

async def install_dependencies():
    """Install essential dependencies."""
    logger.info("📦 Installing essential dependencies...")
    
    essential_deps = [
        "faster-whisper",
        "edge-tts", 
        "webrtcvad",
        "numpy"
    ]
    
    for dep in essential_deps:
        try:
            logger.info(f"  Installing {dep}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep, "--quiet"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info(f"  ✅ {dep} installed")
            else:
                logger.warning(f"  ⚠️ {dep} installation issue")
                
        except Exception as e:
            logger.warning(f"  ⚠️ {dep} failed: {e}")

async def test_stt_engine():
    """Test the STT engine."""
    logger.info("🎤 Testing STT Engine...")
    
    try:
        from faster_whisper import WhisperModel
        
        # Create a simple model
        model = WhisperModel("tiny.en", device="cpu", compute_type="int8")
        
        # Test with dummy audio (1 second of silence)
        import numpy as np
        dummy_audio = np.zeros(16000, dtype=np.float32)
        
        start_time = time.time()
        segments, _ = model.transcribe(dummy_audio, language="en")
        list(segments)  # Consume generator
        processing_time = time.time() - start_time
        
        logger.info(f"  ✅ STT working - Processing time: {processing_time:.3f}s")
        return True
        
    except Exception as e:
        logger.error(f"  ❌ STT test failed: {e}")
        return False

async def test_tts_engine():
    """Test the TTS engine."""
    logger.info("🔊 Testing TTS Engine...")
    
    try:
        import edge_tts
        
        # Test TTS creation
        communicate = edge_tts.Communicate("Hello world", "en-US-AriaNeural")
        
        # Test streaming (just check first chunk)
        start_time = time.time()
        first_chunk_time = None
        
        async for chunk in communicate.stream():
            if chunk["type"] == "audio" and first_chunk_time is None:
                first_chunk_time = time.time()
                break
        
        if first_chunk_time:
            latency = (first_chunk_time - start_time) * 1000
            logger.info(f"  ✅ TTS working - First frame: {latency:.1f}ms")
        else:
            logger.info("  ✅ TTS working - No audio generated (normal for test)")
        
        return True
        
    except Exception as e:
        logger.error(f"  ❌ TTS test failed: {e}")
        return False

async def test_vad():
    """Test Voice Activity Detection."""
    logger.info("🎙️ Testing VAD...")
    
    try:
        import webrtcvad
        
        vad = webrtcvad.Vad(2)
        
        # Test with dummy audio frame
        import numpy as np
        frame = np.zeros(480, dtype=np.int16).tobytes()  # 30ms at 16kHz
        
        is_speech = vad.is_speech(frame, 16000)
        logger.info(f"  ✅ VAD working - Speech detected: {is_speech}")
        return True
        
    except Exception as e:
        logger.error(f"  ❌ VAD test failed: {e}")
        return False

async def demo_voice_pipeline():
    """Demo the complete voice pipeline."""
    logger.info("🎯 Running Voice Pipeline Demo...")
    
    try:
        # Import optimized components
        from advanced_stt_optimizer import AdvancedSTTEngine, STTPerformanceConfig
        from ultra_fast_tts_engine import UltraFastTTSEngine, TTSPerformanceConfig
        
        # Setup STT
        stt_config = STTPerformanceConfig()
        stt_engine = AdvancedSTTEngine(stt_config)
        await stt_engine.initialize()
        
        # Setup TTS
        tts_config = TTSPerformanceConfig()
        tts_engine = UltraFastTTSEngine(tts_config)
        await tts_engine.initialize()
        
        # Demo text
        demo_text = "Hello! This is a demonstration of the optimized voice AI system."
        
        # Test TTS
        logger.info("  🔊 Testing TTS synthesis...")
        start_time = time.time()
        first_chunk_time = None
        chunk_count = 0
        
        async for chunk in tts_engine.synthesize_streaming(demo_text):
            if first_chunk_time is None:
                first_chunk_time = time.time()
            chunk_count += 1
            if chunk_count >= 3:  # Just test first few chunks
                break
        
        if first_chunk_time:
            tts_latency = (first_chunk_time - start_time) * 1000
            logger.info(f"    ✅ TTS first frame: {tts_latency:.1f}ms")
        
        # Test STT with dummy audio
        logger.info("  🎤 Testing STT transcription...")
        import numpy as np
        test_audio = np.random.normal(0, 0.1, 16000).astype(np.float32)  # 1 second
        
        start_time = time.time()
        text, rtf = await stt_engine.transcribe(test_audio)
        stt_time = time.time() - start_time
        
        logger.info(f"    ✅ STT processing: {stt_time*1000:.1f}ms, RTF: {rtf:.3f}")
        
        # Cleanup
        await stt_engine.shutdown()
        await tts_engine.shutdown()
        
        logger.info("  ✅ Voice pipeline demo completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"  ❌ Voice pipeline demo failed: {e}")
        return False

async def main():
    """Main quick start function."""
    print("🚀 QUICK START - OPTIMIZED VOICE AI SYSTEM")
    print("=" * 50)
    
    start_time = time.time()
    
    # Step 1: Install dependencies
    await install_dependencies()
    print()
    
    # Step 2: Test individual components
    stt_ok = await test_stt_engine()
    tts_ok = await test_tts_engine()
    vad_ok = await test_vad()
    print()
    
    # Step 3: Demo complete pipeline (if components work)
    if stt_ok and tts_ok:
        pipeline_ok = await demo_voice_pipeline()
    else:
        logger.warning("🔧 Skipping pipeline demo due to component issues")
        pipeline_ok = False
    
    print()
    
    # Summary
    total_time = time.time() - start_time
    print("📊 QUICK START SUMMARY")
    print("-" * 30)
    print(f"STT Engine: {'✅ Working' if stt_ok else '❌ Issues'}")
    print(f"TTS Engine: {'✅ Working' if tts_ok else '❌ Issues'}")
    print(f"VAD System: {'✅ Working' if vad_ok else '❌ Issues'}")
    print(f"Pipeline: {'✅ Working' if pipeline_ok else '❌ Issues'}")
    print(f"Setup time: {total_time:.1f}s")
    print()
    
    if stt_ok and tts_ok:
        print("🎉 SUCCESS! Your voice AI system is ready!")
        print()
        print("🎯 NEXT STEPS:")
        print("1. Run full test: python run_local_voice_test.py")
        print("2. Try STT engine: python advanced_stt_optimizer.py")
        print("3. Try TTS engine: python ultra_fast_tts_engine.py")
        print("4. Use unified system: python unified_voice_ai_system.py")
        print()
        print("📖 For detailed usage: check VOICE_SYSTEM_USAGE.md")
        
    else:
        print("⚠️ PARTIAL SUCCESS - Some components need attention")
        print()
        print("🔧 TROUBLESHOOTING:")
        if not stt_ok:
            print("- STT: Try 'pip install faster-whisper'")
        if not tts_ok:
            print("- TTS: Try 'pip install edge-tts'")
        if not vad_ok:
            print("- VAD: Try 'pip install webrtcvad'")
        print()
        print("💡 You can still use individual working components!")
    
    print("=" * 50)
    return 0 if (stt_ok and tts_ok) else 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
