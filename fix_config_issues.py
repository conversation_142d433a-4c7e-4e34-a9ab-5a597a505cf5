#!/usr/bin/env python3
"""
🔧 CONFIG ISSUES FIXER
Fixes all configuration conflicts and YAML parsing issues
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConfigFixer:
    """Fixes all configuration issues"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.models_dir = self.base_dir / "models"
        
    def log(self, message: str, level: str = "INFO"):
        """Log message with emoji"""
        emoji_map = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "WARNING": "⚠️",
            "ERROR": "❌"
        }
        print(f"{emoji_map.get(level, 'ℹ️')} {message}")

        # Map SUCCESS to INFO for logging
        log_level = "info" if level == "SUCCESS" else level.lower()
        getattr(logger, log_level)(message)
    
    def create_unified_config(self) -> Dict[str, Any]:
        """Create a unified configuration that works"""
        config = {
            "system": {
                "name": "Fixed Voice AI System",
                "version": "1.0.0",
                "platform": "windows",
                "python_version": "3.12"
            },
            "audio": {
                "sample_rate": 16000,
                "channels": 1,
                "chunk_size": 1024,
                "format": "wav",
                "backend": "sounddevice"
            },
            "stt": {
                "primary_provider": "faster_whisper",
                "fallback_provider": "system",
                "model": "tiny.en",
                "language": "en",
                "device": "cpu"
            },
            "tts": {
                "primary_provider": "edge_tts",
                "fallback_provider": "pyttsx3",
                "system_fallback": "windows_sapi",
                "voice": "en-US-AriaNeural",
                "rate": 200,
                "volume": 0.9
            },
            "llm": {
                "provider": "ollama",
                "base_url": "http://localhost:11434",
                "model": "deepseek-r1:14b",
                "fallback_model": "llama3.2:latest",
                "max_tokens": 150,
                "temperature": 0.7
            },
            "performance": {
                "target_latency_ms": 500,
                "target_memory_mb": 2048,
                "target_cpu_percent": 30,
                "gpu_acceleration": False,
                "streaming_enabled": True,
                "interruption_enabled": True
            },
            "features": {
                "voice_activity_detection": True,
                "noise_suppression": False,
                "echo_cancellation": False,
                "automatic_gain_control": False
            }
        }
        return config
    
    def fix_yaml_config(self):
        """Fix the broken YAML configuration"""
        self.log("🔧 Fixing YAML configuration...")
        
        yaml_file = self.base_dir / "unified_config.yaml"
        
        # Remove the broken YAML file
        if yaml_file.exists():
            yaml_file.unlink()
            self.log("  Removed broken YAML file")
        
        # Create a simple YAML file without complex objects
        simple_yaml_content = """# Fixed Voice AI System Configuration
system:
  name: "Fixed Voice AI System"
  version: "1.0.0"
  platform: "windows"
  python_version: "3.12"

audio:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  format: "wav"
  backend: "sounddevice"

stt:
  primary_provider: "faster_whisper"
  fallback_provider: "system"
  model: "tiny.en"
  language: "en"
  device: "cpu"

tts:
  primary_provider: "edge_tts"
  fallback_provider: "pyttsx3"
  voice: "en-US-AriaNeural"
  rate: 200
  volume: 0.9

llm:
  provider: "ollama"
  base_url: "http://localhost:11434"
  model: "deepseek-r1:14b"
  max_tokens: 150
  temperature: 0.7

performance:
  target_latency_ms: 500
  streaming_enabled: true
  interruption_enabled: true
"""
        
        with open(yaml_file, 'w', encoding='utf-8') as f:
            f.write(simple_yaml_content)
        
        self.log("  ✅ Created fixed YAML configuration", "SUCCESS")
    
    def create_json_configs(self):
        """Create JSON configuration files"""
        self.log("📄 Creating JSON configurations...")
        
        # Ensure models directory exists
        self.models_dir.mkdir(exist_ok=True)
        
        # Main unified config
        unified_config = self.create_unified_config()
        unified_path = self.models_dir / "unified_voice_config_fixed.json"
        
        with open(unified_path, 'w', encoding='utf-8') as f:
            json.dump(unified_config, f, indent=2)
        
        self.log(f"  ✅ Created {unified_path.name}", "SUCCESS")
        
        # Performance config
        performance_config = {
            "optimizations_applied": {
                "audio_buffer_optimization": True,
                "threading_optimization": True,
                "memory_optimization": True,
                "gpu_optimization": False
            },
            "target_latency_ms": 500,
            "target_memory_mb": 2048,
            "target_cpu_percent": 30,
            "streaming_enabled": True
        }
        
        perf_path = self.models_dir / "performance_config_fixed.json"
        with open(perf_path, 'w', encoding='utf-8') as f:
            json.dump(performance_config, f, indent=2)
        
        self.log(f"  ✅ Created {perf_path.name}", "SUCCESS")
        
        # Dependencies config
        deps_config = {
            "required_packages": [
                "faster-whisper>=1.0.0",
                "edge-tts>=7.0.0",
                "sounddevice>=0.4.0",
                "numpy>=1.26.0",
                "ollama>=0.4.0"
            ],
            "optional_packages": [
                "pyttsx3>=2.90",
                "pygame>=2.6.0",
                "webrtcvad>=2.0.0"
            ],
            "system_requirements": {
                "python_version": ">=3.10",
                "memory_gb": 4,
                "disk_space_gb": 2
            }
        }
        
        deps_path = self.models_dir / "dependencies_config.json"
        with open(deps_path, 'w', encoding='utf-8') as f:
            json.dump(deps_config, f, indent=2)
        
        self.log(f"  ✅ Created {deps_path.name}", "SUCCESS")
    
    def backup_old_configs(self):
        """Backup old configuration files"""
        self.log("💾 Backing up old configurations...")
        
        backup_dir = self.base_dir / "config_backup"
        backup_dir.mkdir(exist_ok=True)
        
        config_files = [
            "unified_config.yaml",
            "settings.toml",
            "integration_config.json",
            "models/voice_pipeline_config.json",
            "models/edge_tts_config.json",
            "models/whisper_config.json"
        ]
        
        backed_up = 0
        for config_file in config_files:
            source = self.base_dir / config_file
            if source.exists():
                dest = backup_dir / f"{source.name}.backup"
                try:
                    dest.write_bytes(source.read_bytes())
                    backed_up += 1
                    self.log(f"  ✅ Backed up {config_file}")
                except Exception as e:
                    self.log(f"  ⚠️ Failed to backup {config_file}: {e}", "WARNING")
        
        self.log(f"📊 Backed up {backed_up} configuration files")
    
    def create_settings_toml(self):
        """Create a fixed settings.toml file"""
        self.log("📝 Creating fixed settings.toml...")
        
        toml_content = """# Fixed Voice AI System Settings

[audio]
sample_rate = 16000
chunk_duration = 0.02
vad_level = 2
max_silence = 0.8
min_speech = 0.2

[stt]
model_size = "tiny.en"
beam_size = 1
language = "en"

[tts]
voice = "en-US-AriaNeural"
rate = "+0%"
volume = "+0%"

[llm]
fast_model = "deepseek-r1:14b"
max_tokens = 150
temperature = 0.7

[performance]
audio_buffer_size = 512
max_queue_size = 50
cleanup_retries = 3
response_timeout = 10.0
"""
        
        settings_path = self.base_dir / "settings_fixed.toml"
        with open(settings_path, 'w', encoding='utf-8') as f:
            f.write(toml_content)
        
        self.log(f"  ✅ Created {settings_path.name}", "SUCCESS")
    
    def fix_all_configs(self) -> bool:
        """Fix all configuration issues"""
        self.log("🚀 FIXING ALL CONFIGURATION ISSUES")
        self.log("=" * 60)
        
        try:
            # Step 1: Backup old configs
            self.backup_old_configs()
            
            # Step 2: Fix YAML issues
            self.fix_yaml_config()
            
            # Step 3: Create JSON configs
            self.create_json_configs()
            
            # Step 4: Create fixed TOML
            self.create_settings_toml()
            
            self.log("=" * 60)
            self.log("✅ ALL CONFIGURATION ISSUES FIXED!", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"❌ Configuration fix failed: {e}", "ERROR")
            return False

def main():
    """Main function"""
    fixer = ConfigFixer()
    success = fixer.fix_all_configs()
    
    if success:
        print("\n🎉 SUCCESS! Configuration issues are now fixed.")
        print("🎯 Next steps:")
        print("  1. Run: python voice_system_fixed.py")
        print("  2. Test voice conversations!")
    else:
        print("\n❌ FAILED! Some configuration issues remain.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
