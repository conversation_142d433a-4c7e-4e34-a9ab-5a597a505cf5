#!/usr/bin/env python3
"""
SIMPLE VOICE SYSTEM TEST
Basic test to verify your voice AI system is working
"""

import time
import sys

def test_basic_imports():
    """Test basic Python functionality."""
    print("🔧 Testing Basic Imports...")
    
    try:
        import asyncio
        print("  ✅ asyncio: OK")
    except ImportError:
        print("  ❌ asyncio: Missing")
        return False
    
    try:
        import numpy as np
        print("  ✅ numpy: OK")
    except ImportError:
        print("  ❌ numpy: Missing (run: pip install numpy)")
        return False
    
    try:
        import logging
        print("  ✅ logging: OK")
    except ImportError:
        print("  ❌ logging: Missing")
        return False
    
    return True

def test_voice_dependencies():
    """Test voice-specific dependencies."""
    print("\n🎤 Testing Voice Dependencies...")
    
    deps_ok = True
    
    try:
        import faster_whisper
        print("  ✅ faster-whisper: OK")
    except ImportError:
        print("  ❌ faster-whisper: Missing (run: pip install faster-whisper)")
        deps_ok = False
    
    try:
        import edge_tts
        print("  ✅ edge-tts: OK")
    except ImportError:
        print("  ❌ edge-tts: Missing (run: pip install edge-tts)")
        deps_ok = False
    
    try:
        import webrtcvad
        print("  ✅ webrtcvad: OK")
    except ImportError:
        print("  ❌ webrtcvad: Missing (run: pip install webrtcvad)")
        deps_ok = False
    
    return deps_ok

def test_optimized_components():
    """Test if optimized components are available."""
    print("\n⚡ Testing Optimized Components...")
    
    components_ok = True
    
    # Test STT optimizer
    try:
        from advanced_stt_optimizer import AdvancedSTTEngine
        print("  ✅ Advanced STT Engine: Available")
    except ImportError as e:
        print(f"  ❌ Advanced STT Engine: Missing ({e})")
        components_ok = False
    
    # Test TTS optimizer
    try:
        from ultra_fast_tts_engine import UltraFastTTSEngine
        print("  ✅ Ultra-Fast TTS Engine: Available")
    except ImportError as e:
        print(f"  ❌ Ultra-Fast TTS Engine: Missing ({e})")
        components_ok = False
    
    # Test unified system
    try:
        from unified_voice_ai_system import UnifiedVoiceAI
        print("  ✅ Unified Voice AI System: Available")
    except ImportError as e:
        print(f"  ❌ Unified Voice AI System: Missing ({e})")
        components_ok = False
    
    return components_ok

async def test_stt_engine():
    """Test STT engine functionality."""
    print("\n🎤 Testing STT Engine...")
    
    try:
        from advanced_stt_optimizer import AdvancedSTTEngine, STTPerformanceConfig
        import numpy as np
        
        # Create engine
        config = STTPerformanceConfig()
        engine = AdvancedSTTEngine(config)
        
        # Initialize
        success = await engine.initialize()
        if not success:
            print("  ❌ STT Engine initialization failed")
            return False
        
        # Test with dummy audio
        test_audio = np.zeros(16000, dtype=np.float32)  # 1 second silence
        
        start_time = time.time()
        text, rtf = await engine.transcribe(test_audio)
        processing_time = time.time() - start_time
        
        print(f"  ✅ STT Engine working")
        print(f"     Processing time: {processing_time:.3f}s")
        print(f"     RTF: {rtf:.3f}")
        print(f"     Transcription: '{text}'")
        
        await engine.shutdown()
        return True
        
    except Exception as e:
        print(f"  ❌ STT Engine test failed: {e}")
        return False

async def test_tts_engine():
    """Test TTS engine functionality."""
    print("\n🔊 Testing TTS Engine...")
    
    try:
        from ultra_fast_tts_engine import UltraFastTTSEngine, TTSPerformanceConfig
        
        # Create engine
        config = TTSPerformanceConfig()
        engine = UltraFastTTSEngine(config)
        
        # Initialize
        success = await engine.initialize()
        if not success:
            print("  ❌ TTS Engine initialization failed")
            return False
        
        # Test synthesis
        test_text = "Hello, this is a test."
        
        start_time = time.time()
        first_chunk_time = None
        chunk_count = 0
        
        async for chunk in engine.synthesize_streaming(test_text):
            if first_chunk_time is None:
                first_chunk_time = time.time()
            chunk_count += 1
            if chunk_count >= 3:  # Just test first few chunks
                break
        
        if first_chunk_time:
            first_frame_latency = (first_chunk_time - start_time) * 1000
            print(f"  ✅ TTS Engine working")
            print(f"     First frame latency: {first_frame_latency:.1f}ms")
            print(f"     Chunks generated: {chunk_count}")
        else:
            print("  ⚠️ TTS Engine working but no audio generated")
        
        await engine.shutdown()
        return True
        
    except Exception as e:
        print(f"  ❌ TTS Engine test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 VOICE AI SYSTEM TEST")
    print("=" * 40)
    
    # Test 1: Basic imports
    basic_ok = test_basic_imports()
    
    # Test 2: Voice dependencies
    deps_ok = test_voice_dependencies()
    
    # Test 3: Optimized components
    components_ok = test_optimized_components()
    
    # Test 4: STT engine (if available)
    stt_ok = False
    if basic_ok and deps_ok and components_ok:
        stt_ok = await test_stt_engine()
    
    # Test 5: TTS engine (if available)
    tts_ok = False
    if basic_ok and deps_ok and components_ok:
        tts_ok = await test_tts_engine()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("-" * 30)
    print(f"Basic imports: {'✅ OK' if basic_ok else '❌ Failed'}")
    print(f"Dependencies: {'✅ OK' if deps_ok else '❌ Failed'}")
    print(f"Components: {'✅ OK' if components_ok else '❌ Failed'}")
    print(f"STT Engine: {'✅ OK' if stt_ok else '❌ Failed'}")
    print(f"TTS Engine: {'✅ OK' if tts_ok else '❌ Failed'}")
    
    if stt_ok and tts_ok:
        print("\n🎉 SUCCESS! Your voice AI system is working!")
        print("\n🎯 NEXT STEPS:")
        print("1. Run full test: python run_local_voice_test.py")
        print("2. Try unified system: python unified_voice_ai_system.py")
        print("3. Run benchmarks: python stt_performance_benchmark.py")
        
    elif basic_ok and deps_ok:
        print("\n⚠️ PARTIAL SUCCESS - Core dependencies working")
        print("\n🔧 TO FIX:")
        if not stt_ok:
            print("- STT: Check faster-whisper installation")
        if not tts_ok:
            print("- TTS: Check edge-tts installation")
        
    else:
        print("\n❌ SETUP NEEDED")
        print("\n🔧 TO FIX:")
        if not basic_ok:
            print("- Install Python 3.8+ with numpy")
        if not deps_ok:
            print("- Run: pip install faster-whisper edge-tts webrtcvad")
        if not components_ok:
            print("- Make sure all voice system files are present")
    
    print("=" * 40)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
