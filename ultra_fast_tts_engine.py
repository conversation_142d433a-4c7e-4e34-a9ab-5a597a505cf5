#!/usr/bin/env python3
"""
ULTRA-FAST TTS ENGINE
High-performance Text-to-Speech with sub-100ms first frame latency
"""

import asyncio
import logging
import time
import threading
from typing import Dict, Any, Optional, List, AsyncGenerator, Callable
from dataclasses import dataclass
from enum import Enum
import numpy as np
from collections import deque
import tempfile
import os
import weakref

logger = logging.getLogger(__name__)

class TTSProvider(Enum):
    """Available TTS providers with performance characteristics."""
    EDGE_TTS = "edge_tts"        # ~200ms first frame, high quality
    PYTTSX3 = "pyttsx3"          # ~500ms first frame, local
    WINDOWS_SAPI = "windows_sapi" # ~300ms first frame, Windows only
    CACHED = "cached"            # ~10ms first frame, pre-generated

class TTSQuality(Enum):
    """TTS quality levels."""
    SPEED = "speed"      # Optimized for minimum latency
    BALANCED = "balanced" # Good balance of speed and quality
    QUALITY = "quality"   # Maximum quality, higher latency

@dataclass
class TTSPerformanceConfig:
    """Ultra-fast TTS performance configuration."""
    # Provider selection
    primary_provider: TTSProvider = TTSProvider.EDGE_TTS
    fallback_providers: List[TTSProvider] = None
    
    # Performance targets
    first_frame_target_ms: float = 100.0
    chunk_size_ms: float = 30.0  # Smaller chunks for faster streaming
    max_latency_ms: float = 500.0
    
    # Quality settings
    quality_mode: TTSQuality = TTSQuality.SPEED
    sample_rate: int = 22050
    channels: int = 1
    
    # Optimization features
    enable_caching: bool = True
    enable_pregeneration: bool = True
    enable_concurrent_synthesis: bool = True
    enable_buffer_preallocation: bool = True
    
    # Caching configuration
    cache_size: int = 100
    cache_common_phrases: bool = True
    pregenerate_responses: bool = True
    
    # Buffer configuration
    buffer_pool_size: int = 50
    chunk_buffer_size: int = 4096
    
    # Edge TTS specific
    edge_voice: str = "en-US-AriaNeural"
    edge_rate: str = "+20%"  # Faster speech rate
    edge_pitch: str = "+0Hz"
    
    def __post_init__(self):
        if self.fallback_providers is None:
            self.fallback_providers = [TTSProvider.PYTTSX3, TTSProvider.WINDOWS_SAPI]

class TTSCache:
    """High-performance TTS cache with LRU eviction."""
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.cache: Dict[str, bytes] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        self.hit_count = 0
        self.miss_count = 0
        
    def get(self, text_hash: str) -> Optional[bytes]:
        """Get cached audio data."""
        with self.lock:
            if text_hash in self.cache:
                self.access_times[text_hash] = time.time()
                self.hit_count += 1
                return self.cache[text_hash]
            
            self.miss_count += 1
            return None
    
    def put(self, text_hash: str, audio_data: bytes) -> None:
        """Cache audio data with LRU eviction."""
        with self.lock:
            # Evict if cache is full
            if len(self.cache) >= self.max_size and text_hash not in self.cache:
                lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
                del self.cache[lru_key]
                del self.access_times[lru_key]
            
            self.cache[text_hash] = audio_data
            self.access_times[text_hash] = time.time()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / max(total_requests, 1)
        
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "hit_rate": hit_rate
        }

class AudioBufferPool:
    """Pre-allocated audio buffer pool for zero-allocation streaming."""
    
    def __init__(self, pool_size: int = 50, buffer_size: int = 4096):
        self.pool_size = pool_size
        self.buffer_size = buffer_size
        self.available_buffers = deque()
        self.in_use_buffers = weakref.WeakSet()
        self.lock = threading.Lock()
        
        # Pre-allocate buffers
        self._preallocate_buffers()
    
    def _preallocate_buffers(self) -> None:
        """Pre-allocate audio buffers."""
        for _ in range(self.pool_size):
            buffer = bytearray(self.buffer_size)
            self.available_buffers.append(buffer)
    
    def get_buffer(self, size: int = None) -> bytearray:
        """Get a buffer from the pool."""
        if size is None:
            size = self.buffer_size
        
        with self.lock:
            if self.available_buffers and size <= self.buffer_size:
                buffer = self.available_buffers.popleft()
                self.in_use_buffers.add(buffer)
                return buffer[:size]
            
            # Create new buffer if pool is empty
            buffer = bytearray(max(size, self.buffer_size))
            self.in_use_buffers.add(buffer)
            return buffer[:size]
    
    def return_buffer(self, buffer: bytearray) -> None:
        """Return buffer to pool."""
        with self.lock:
            if len(self.available_buffers) < self.pool_size:
                # Clear buffer and return to pool
                buffer[:] = b'\x00' * len(buffer)
                self.available_buffers.append(buffer)
            
            self.in_use_buffers.discard(buffer)

class UltraFastTTSEngine:
    """Ultra-high performance TTS engine with sub-100ms first frame latency."""
    
    def __init__(self, config: TTSPerformanceConfig = None):
        self.config = config or TTSPerformanceConfig()
        self.cache = TTSCache(self.config.cache_size) if self.config.enable_caching else None
        self.buffer_pool = AudioBufferPool(
            self.config.buffer_pool_size, 
            self.config.chunk_buffer_size
        ) if self.config.enable_buffer_preallocation else None
        
        # Performance tracking
        self.stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "avg_first_frame_ms": 0.0,
            "avg_total_latency_ms": 0.0,
            "provider_usage": {}
        }
        
        # Provider instances
        self._providers = {}
        self._provider_lock = threading.RLock()
        
        # Common phrases for pre-generation
        self.common_phrases = [
            "I understand",
            "Tell me more",
            "That's interesting",
            "I see",
            "Go on",
            "What else?",
            "I'm listening",
            "Please continue",
            "That makes sense",
            "I agree"
        ]
        
        # Background tasks
        self._pregeneration_task = None
        self._running = False
        
        logger.info("Ultra-Fast TTS Engine initialized")
    
    async def initialize(self) -> bool:
        """Initialize the TTS engine."""
        try:
            logger.info("🔊 Initializing Ultra-Fast TTS Engine...")
            
            # Initialize providers
            await self._initialize_providers()
            
            # Pre-generate common phrases
            if self.config.enable_pregeneration:
                await self._pregenerate_common_phrases()
            
            # Start background tasks
            self._running = True
            self._pregeneration_task = asyncio.create_task(self._background_pregeneration())
            
            logger.info("✅ Ultra-Fast TTS Engine ready")
            return True
            
        except Exception as e:
            logger.error(f"❌ TTS Engine initialization failed: {e}")
            return False
    
    async def _initialize_providers(self) -> None:
        """Initialize TTS providers."""
        # Initialize Edge TTS
        if self.config.primary_provider == TTSProvider.EDGE_TTS:
            try:
                import edge_tts
                self._providers[TTSProvider.EDGE_TTS] = edge_tts
                logger.info("  ✅ Edge TTS provider initialized")
            except ImportError:
                logger.warning("  ⚠️ Edge TTS not available")
        
        # Initialize pyttsx3
        if TTSProvider.PYTTSX3 in self.config.fallback_providers:
            try:
                import pyttsx3
                self._providers[TTSProvider.PYTTSX3] = pyttsx3
                logger.info("  ✅ pyttsx3 provider initialized")
            except ImportError:
                logger.warning("  ⚠️ pyttsx3 not available")
    
    async def _pregenerate_common_phrases(self) -> None:
        """Pre-generate common phrases for instant response."""
        if not self.cache:
            return
        
        logger.info("  🚀 Pre-generating common phrases...")
        
        for phrase in self.common_phrases:
            try:
                text_hash = self._hash_text(phrase)
                if not self.cache.get(text_hash):
                    audio_data = await self._synthesize_with_provider(phrase, TTSProvider.EDGE_TTS)
                    if audio_data:
                        self.cache.put(text_hash, audio_data)
                        logger.debug(f"    Pre-generated: '{phrase}'")
            except Exception as e:
                logger.warning(f"    Failed to pre-generate '{phrase}': {e}")
        
        logger.info(f"  ✅ Pre-generated {len(self.common_phrases)} common phrases")
    
    async def _background_pregeneration(self) -> None:
        """Background task for continuous pre-generation."""
        while self._running:
            try:
                # Sleep for a while
                await asyncio.sleep(300)  # 5 minutes
                
                # Re-generate expired cache entries
                if self.cache:
                    cache_stats = self.cache.get_stats()
                    if cache_stats["hit_rate"] > 0.8:  # High hit rate, expand cache
                        # Could add more phrases here
                        pass
                
            except Exception as e:
                logger.error(f"Background pregeneration error: {e}")
                await asyncio.sleep(60)
    
    def _hash_text(self, text: str) -> str:
        """Generate hash for text caching."""
        import hashlib
        # Include voice settings in hash
        hash_input = f"{text}_{self.config.edge_voice}_{self.config.edge_rate}_{self.config.quality_mode.value}"
        return hashlib.md5(hash_input.encode()).hexdigest()
    
    async def synthesize_streaming(self, text: str) -> AsyncGenerator[bytes, None]:
        """Synthesize text with ultra-low latency streaming."""
        start_time = time.time()
        first_frame_time = None
        
        try:
            # Check cache first
            if self.cache:
                text_hash = self._hash_text(text)
                cached_audio = self.cache.get(text_hash)
                if cached_audio:
                    self.stats["cache_hits"] += 1
                    
                    # Stream cached audio in chunks
                    chunk_size = self.config.chunk_buffer_size
                    for i in range(0, len(cached_audio), chunk_size):
                        chunk = cached_audio[i:i + chunk_size]
                        if first_frame_time is None:
                            first_frame_time = time.time()
                        yield chunk
                        
                        # Small delay to simulate streaming
                        await asyncio.sleep(0.01)
                    
                    self._update_stats(start_time, first_frame_time, "cached")
                    return
            
            self.stats["cache_misses"] += 1
            
            # Synthesize with primary provider
            audio_data = await self._synthesize_with_streaming(text)
            
            # Cache the result
            if self.cache and audio_data:
                text_hash = self._hash_text(text)
                # Convert generator to bytes for caching
                full_audio = b""
                async for chunk in audio_data:
                    if first_frame_time is None:
                        first_frame_time = time.time()
                    full_audio += chunk
                    yield chunk
                
                self.cache.put(text_hash, full_audio)
            else:
                # Stream without caching
                async for chunk in audio_data:
                    if first_frame_time is None:
                        first_frame_time = time.time()
                    yield chunk
            
            self._update_stats(start_time, first_frame_time, self.config.primary_provider.value)
            
        except Exception as e:
            logger.error(f"TTS synthesis failed: {e}")
            # Try fallback providers
            for provider in self.config.fallback_providers:
                try:
                    audio_data = await self._synthesize_with_provider(text, provider)
                    if audio_data:
                        if first_frame_time is None:
                            first_frame_time = time.time()
                        yield audio_data
                        self._update_stats(start_time, first_frame_time, provider.value)
                        return
                except Exception as fallback_error:
                    logger.warning(f"Fallback provider {provider.value} failed: {fallback_error}")
            
            raise Exception("All TTS providers failed")
    
    async def _synthesize_with_streaming(self, text: str) -> AsyncGenerator[bytes, None]:
        """Synthesize with streaming using primary provider."""
        if self.config.primary_provider == TTSProvider.EDGE_TTS:
            async for chunk in self._synthesize_edge_tts_streaming(text):
                yield chunk
        else:
            # Fallback to non-streaming
            audio_data = await self._synthesize_with_provider(text, self.config.primary_provider)
            if audio_data:
                # Split into chunks for streaming
                chunk_size = self.config.chunk_buffer_size
                for i in range(0, len(audio_data), chunk_size):
                    yield audio_data[i:i + chunk_size]
                    await asyncio.sleep(0.01)  # Simulate streaming delay
    
    async def _synthesize_edge_tts_streaming(self, text: str) -> AsyncGenerator[bytes, None]:
        """Ultra-fast Edge TTS streaming synthesis."""
        try:
            edge_tts = self._providers.get(TTSProvider.EDGE_TTS)
            if not edge_tts:
                raise Exception("Edge TTS not available")
            
            # Create communicate object with optimized settings
            communicate = edge_tts.Communicate(
                text, 
                self.config.edge_voice,
                rate=self.config.edge_rate,
                pitch=self.config.edge_pitch
            )
            
            # Stream audio chunks
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    yield chunk["data"]
                    
        except Exception as e:
            logger.error(f"Edge TTS streaming failed: {e}")
            raise
    
    async def _synthesize_with_provider(self, text: str, provider: TTSProvider) -> Optional[bytes]:
        """Synthesize with specific provider (non-streaming)."""
        try:
            if provider == TTSProvider.EDGE_TTS:
                return await self._synthesize_edge_tts(text)
            elif provider == TTSProvider.PYTTSX3:
                return await self._synthesize_pyttsx3(text)
            elif provider == TTSProvider.WINDOWS_SAPI:
                return await self._synthesize_windows_sapi(text)
            else:
                raise Exception(f"Unknown provider: {provider}")
                
        except Exception as e:
            logger.error(f"Provider {provider.value} synthesis failed: {e}")
            return None
    
    async def _synthesize_edge_tts(self, text: str) -> bytes:
        """Synthesize using Edge TTS."""
        edge_tts = self._providers.get(TTSProvider.EDGE_TTS)
        if not edge_tts:
            raise Exception("Edge TTS not available")
        
        communicate = edge_tts.Communicate(
            text, 
            self.config.edge_voice,
            rate=self.config.edge_rate,
            pitch=self.config.edge_pitch
        )
        
        audio_data = b""
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]
        
        return audio_data
    
    async def _synthesize_pyttsx3(self, text: str) -> bytes:
        """Synthesize using pyttsx3."""
        pyttsx3 = self._providers.get(TTSProvider.PYTTSX3)
        if not pyttsx3:
            raise Exception("pyttsx3 not available")
        
        # Use temporary file for synthesis
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        try:
            # Run synthesis in thread to avoid blocking
            def synthesis_worker():
                engine = pyttsx3.init()
                engine.setProperty("rate", 200)  # Faster speech
                engine.setProperty("volume", 0.9)
                engine.save_to_file(text, temp_path)
                engine.runAndWait()
            
            # Run in thread
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, synthesis_worker)
            
            # Read audio data
            with open(temp_path, "rb") as f:
                return f.read()
                
        finally:
            # Cleanup
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    async def _synthesize_windows_sapi(self, text: str) -> bytes:
        """Synthesize using Windows SAPI."""
        try:
            import win32com.client
            
            # Create SAPI voice
            voice = win32com.client.Dispatch("SAPI.SpVoice")
            
            # Set faster rate
            voice.Rate = 2  # Faster speech
            
            # Use temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                temp_path = tmp_file.name
            
            try:
                # Create file stream
                file_stream = win32com.client.Dispatch("SAPI.SpFileStream")
                file_stream.Open(temp_path, 3)  # Write mode
                voice.AudioOutputStream = file_stream
                
                # Synthesize
                voice.Speak(text)
                file_stream.Close()
                
                # Read audio data
                with open(temp_path, "rb") as f:
                    return f.read()
                    
            finally:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except ImportError:
            raise Exception("Windows SAPI not available")
    
    def _update_stats(self, start_time: float, first_frame_time: Optional[float], provider: str) -> None:
        """Update performance statistics."""
        total_latency = (time.time() - start_time) * 1000  # ms
        first_frame_latency = (first_frame_time - start_time) * 1000 if first_frame_time else 0
        
        self.stats["total_requests"] += 1
        
        # Update averages
        n = self.stats["total_requests"]
        self.stats["avg_first_frame_ms"] = (
            (self.stats["avg_first_frame_ms"] * (n - 1) + first_frame_latency) / n
        )
        self.stats["avg_total_latency_ms"] = (
            (self.stats["avg_total_latency_ms"] * (n - 1) + total_latency) / n
        )
        
        # Update provider usage
        if provider not in self.stats["provider_usage"]:
            self.stats["provider_usage"][provider] = 0
        self.stats["provider_usage"][provider] += 1
        
        logger.debug(f"TTS latency - First frame: {first_frame_latency:.1f}ms, Total: {total_latency:.1f}ms")
    
    async def shutdown(self) -> None:
        """Shutdown the TTS engine."""
        logger.info("🛑 Shutting down Ultra-Fast TTS Engine...")
        
        self._running = False
        
        if self._pregeneration_task:
            self._pregeneration_task.cancel()
        
        logger.info("✅ Ultra-Fast TTS Engine shutdown complete")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        cache_stats = self.cache.get_stats() if self.cache else {}
        
        return {
            "performance_stats": self.stats.copy(),
            "cache_stats": cache_stats,
            "config": {
                "primary_provider": self.config.primary_provider.value,
                "first_frame_target_ms": self.config.first_frame_target_ms,
                "quality_mode": self.config.quality_mode.value,
                "caching_enabled": self.config.enable_caching
            }
        }
    
    def optimize_for_speed(self) -> None:
        """Optimize configuration for maximum speed."""
        self.config.quality_mode = TTSQuality.SPEED
        self.config.first_frame_target_ms = 50.0
        self.config.chunk_size_ms = 20.0
        self.config.edge_rate = "+30%"  # Even faster speech
        self.config.enable_caching = True
        self.config.enable_pregeneration = True
        
        logger.info("🚀 TTS optimized for maximum speed")
    
    def optimize_for_quality(self) -> None:
        """Optimize configuration for maximum quality."""
        self.config.quality_mode = TTSQuality.QUALITY
        self.config.first_frame_target_ms = 200.0
        self.config.chunk_size_ms = 50.0
        self.config.edge_rate = "+0%"  # Normal speech rate
        
        logger.info("🎯 TTS optimized for maximum quality")


async def main():
    """Test the ultra-fast TTS engine."""
    logging.basicConfig(level=logging.INFO)
    
    logger.info("🔊 TESTING ULTRA-FAST TTS ENGINE")
    
    try:
        # Create TTS engine with speed optimization
        config = TTSPerformanceConfig()
        engine = UltraFastTTSEngine(config)
        
        # Optimize for speed
        engine.optimize_for_speed()
        
        # Initialize
        success = await engine.initialize()
        if not success:
            logger.error("Failed to initialize TTS engine")
            return 1
        
        # Test synthesis
        test_phrases = [
            "Hello, this is a test of the ultra-fast TTS engine.",
            "I understand",  # Should be cached
            "Tell me more",  # Should be cached
            "This is a longer sentence to test streaming performance and latency optimization."
        ]
        
        for i, phrase in enumerate(test_phrases):
            logger.info(f"Testing phrase {i+1}: '{phrase}'")
            
            start_time = time.time()
            first_chunk_time = None
            chunk_count = 0
            
            async for chunk in engine.synthesize_streaming(phrase):
                if first_chunk_time is None:
                    first_chunk_time = time.time()
                chunk_count += 1
            
            total_time = time.time() - start_time
            first_frame_latency = (first_chunk_time - start_time) * 1000 if first_chunk_time else 0
            
            logger.info(f"  First frame: {first_frame_latency:.1f}ms, Total: {total_time*1000:.1f}ms, Chunks: {chunk_count}")
        
        # Get performance stats
        stats = engine.get_performance_stats()
        
        print("\n" + "="*50)
        print("🔊 ULTRA-FAST TTS PERFORMANCE RESULTS")
        print("="*50)
        print(f"Average first frame latency: {stats['performance_stats']['avg_first_frame_ms']:.1f}ms")
        print(f"Average total latency: {stats['performance_stats']['avg_total_latency_ms']:.1f}ms")
        print(f"Cache hit rate: {stats['cache_stats'].get('hit_rate', 0):.2%}")
        print(f"Total requests: {stats['performance_stats']['total_requests']}")
        print("="*50)
        
        # Shutdown
        await engine.shutdown()
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ TTS engine test failed: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
