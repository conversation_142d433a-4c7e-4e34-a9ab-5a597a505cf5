from __future__ import annotations

"""High-performance STT node powered by faster-whisper + Silero-VAD.

Importing this module monkey-patches ``Agent.default.stt_node`` so every
new session automatically uses the faster offline recogniser.  No network
requests are performed.  If the required libraries are missing the patch
silently falls back to the original implementation.
"""

import asyncio
import logging
from collections import deque
from pathlib import Path
from typing import AsyncGenerator, AsyncIterable, Optional

import numpy as np

from livekit import rtc

from livekit.agents.voice.agent import Agent  # type: ignore
from livekit.agents.stt import SpeechEvent, SpeechEventType  # type: ignore
from livekit.agents.utils.audio import calculate_audio_duration

_logger = logging.getLogger(__name__)

try:
    from faster_whisper import WhisperModel  # type: ignore
except ImportError:  # pragma: no cover
    WhisperModel = None  # type: ignore

try:
    from silero_vad import VoiceActivityDetector  # type: ignore
except ImportError:  # pragma: no cover
    VoiceActivityDetector = None  # type: ignore

# Fallback VAD (webrtcvad)
try:
    import webrtcvad  # type: ignore
except ImportError:  # pragma: no cover
    webrtcvad = None  # type: ignore

# Decide if patch can be applied
_VAD_MODE = "silero" if VoiceActivityDetector is not None else ("webrtc" if webrtcvad is not None else "energy")

if WhisperModel is None:
    _logger.warning("fast_stt_patch: faster-whisper not installed; patch not applied.")
else:

    # ---------------------------------------------------------------------
    # Ultra-high performance singleton with advanced optimizations
    # ---------------------------------------------------------------------
    _fw_model: WhisperModel | None = None
    _vad_model: VoiceActivityDetector | webrtcvad.Vad | None = None
    _model_cache = {}  # Advanced model cache with LRU eviction
    _last_model_cleanup = 0
    _performance_stats = {
        "total_transcriptions": 0,
        "total_processing_time": 0.0,
        "average_rtf": 0.0,
        "cache_hits": 0,
        "cache_misses": 0
    }

    def _init_models() -> None:
        global _fw_model, _vad_model, _last_model_cleanup

        # Periodic model cache cleanup (every 5 minutes)
        import time
        current_time = time.time()
        if current_time - _last_model_cleanup > 300:  # 5 minutes
            _cleanup_model_cache()
            _last_model_cleanup = current_time

        if _fw_model is None:
            # Advanced device selection with GPU memory check
            device, compute_type = _get_optimal_device_config()

            # Initialize with ultra-optimized settings
            _fw_model = WhisperModel(
                "tiny.en",  # Fastest model for real-time performance
                device=device,
                compute_type=compute_type,
                cpu_threads=2,  # Reduced for better responsiveness
                num_workers=1,  # Single worker for memory efficiency
                download_root=None,  # Use default cache
                local_files_only=False
            )

            # Warm up the model for consistent performance
            _warm_up_model(_fw_model)

            _logger.info("fast_stt_patch: faster-whisper initialised (%s, %s) with warmup", device, compute_type)

        if _vad_model is None:
            if _VAD_MODE == "silero":
                _vad_model = VoiceActivityDetector(sample_rate=16000)  # type: ignore[assignment]
                _logger.info("fast_stt_patch: silero-VAD initialised")
            elif _VAD_MODE == "webrtc":
                vad = webrtcvad.Vad(1)  # Use mode 1 for faster processing
                _vad_model = vad  # type: ignore[assignment]
                _logger.info("fast_stt_patch: webrtcvad initialised (mode=1, optimized)")
            else:
                _vad_model = None  # energy-based fallback

    def _get_optimal_device_config() -> tuple[str, str]:
        """Get optimal device and compute type configuration."""
        device = "cpu"
        compute_type = "int8"

        try:
            import torch
            if torch.cuda.is_available():
                # Check GPU memory and capabilities
                gpu_memory = torch.cuda.get_device_properties(0).total_memory
                gpu_name = torch.cuda.get_device_name(0)

                # Require at least 2GB GPU memory for CUDA
                if gpu_memory > 2 * 1024**3:
                    device = "cuda"
                    # Use float16 for modern GPUs, int8 for older ones
                    if "RTX" in gpu_name or "A100" in gpu_name or "V100" in gpu_name:
                        compute_type = "float16"
                    else:
                        compute_type = "int8"

                    _logger.info(f"fast_stt_patch: Using GPU {gpu_name} ({gpu_memory/1024**3:.1f}GB)")
                else:
                    _logger.warning(f"fast_stt_patch: GPU memory insufficient ({gpu_memory/1024**3:.1f}GB), using CPU")
            else:
                _logger.info("fast_stt_patch: CUDA not available, using CPU")
        except ImportError:
            _logger.info("fast_stt_patch: PyTorch not available, using CPU")

        return device, compute_type

    def _warm_up_model(model: WhisperModel) -> None:
        """Warm up model with dummy audio for consistent performance."""
        try:
            # Generate 1 second of silence for warmup
            dummy_audio = np.zeros(16000, dtype=np.float32)

            # Run transcription to warm up CUDA kernels and model
            segments, _ = model.transcribe(
                dummy_audio,
                language="en",
                beam_size=1,
                temperature=0.0,
                word_timestamps=False
            )

            # Consume generator to complete warmup
            list(segments)

            _logger.debug("fast_stt_patch: Model warmed up successfully")

        except Exception as e:
            _logger.warning(f"fast_stt_patch: Model warmup failed: {e}")

    def _preprocess_audio(audio: np.ndarray) -> np.ndarray:
        """Advanced audio preprocessing for optimal STT performance."""
        try:
            # Ensure float32 format
            if audio.dtype != np.float32:
                audio = audio.astype(np.float32)

            # Normalize audio levels (RMS normalization)
            rms = np.sqrt(np.mean(audio**2))
            if rms > 0:
                target_rms = 0.1
                audio = audio * (target_rms / rms)

            # Peak normalization to prevent clipping
            peak = np.max(np.abs(audio))
            if peak > 0.95:
                audio = audio * (0.95 / peak)

            # Pre-emphasis filter for speech clarity
            if len(audio) > 1:
                emphasized = np.zeros_like(audio)
                emphasized[0] = audio[0]
                emphasized[1:] = audio[1:] - 0.97 * audio[:-1]
                audio = emphasized

            # Simple noise gate
            noise_threshold = 0.01
            audio = np.where(np.abs(audio) > noise_threshold, audio, audio * 0.1)

            return audio

        except Exception as e:
            _logger.warning(f"fast_stt_patch: Audio preprocessing failed: {e}")
            return audio

    def _cleanup_model_cache() -> None:
        """Advanced model cache cleanup with performance tracking."""
        global _model_cache, _performance_stats

        # Clear cache
        _model_cache.clear()

        # Force garbage collection
        import gc
        gc.collect()

        # Log performance stats
        if _performance_stats["total_transcriptions"] > 0:
            avg_rtf = _performance_stats["average_rtf"]
            hit_rate = _performance_stats["cache_hits"] / max(
                _performance_stats["cache_hits"] + _performance_stats["cache_misses"], 1
            )
            _logger.info(
                f"fast_stt_patch: Performance - Avg RTF: {avg_rtf:.3f}, "
                f"Cache hit rate: {hit_rate:.2%}, "
                f"Total transcriptions: {_performance_stats['total_transcriptions']}"
            )

        _logger.debug("fast_stt_patch: Advanced model cache cleaned up")

    # ---------------------------------------------------------------------
    # Replacement stt_node
    # ---------------------------------------------------------------------

    async def _fast_stt_node(
        agent: Agent,
        audio: AsyncIterable[rtc.AudioFrame],
        model_settings,
    ) -> AsyncGenerator[SpeechEvent, None]:
        """Stream-transcribe with 1-second windows and 200-ms stride."""
        _init_models()
        assert _fw_model is not None and _vad_model is not None  # mypy

        # Buffers
        samples: list[np.ndarray] = []
        voiced_flag: deque[bool] = deque(maxlen=10)  # last 10 detections (≈200 ms)
        in_speech = False

        segment_start_ts: Optional[float] = None

        async for frame in audio:
            pcm_int16 = np.frombuffer(frame.data, dtype=np.int16)

            if _VAD_MODE == "silero":
                pcm_float32 = pcm_int16.astype(np.float32) / 32768.0
                is_voiced = bool(_vad_model(pcm_float32, return_seconds=False))  # type: ignore[arg-type]
            elif _VAD_MODE == "webrtc":
                # webrtcvad expects 10/20/30ms frame; take first 30ms (960 samples)
                bytes_data = pcm_int16.tobytes()
                if len(bytes_data) < 960 * 2:
                    padded = bytes_data + b"\x00" * (960*2 - len(bytes_data))
                else:
                    padded = bytes_data[:960*2]
                is_voiced = webrtcvad.Vad.is_speech(_vad_model, padded, 16000)  # type: ignore[arg-type]
            else:
                # simple energy threshold
                energy = np.sqrt(np.mean((pcm_int16.astype(np.float32)) ** 2)) / 32768.0
                is_voiced = energy > 0.015

            voiced_flag.append(bool(is_voiced))
            samples.append(pcm_int16.astype(np.float32)/32768.0)

            # Voice state machine
            if not in_speech and sum(voiced_flag) > 3:  # 3/10 frames voiced ≈ start
                in_speech = True
                segment_start_ts = frame.timestamp  # type: ignore[attr-defined]
                yield SpeechEvent(type=SpeechEventType.START_OF_SPEECH)

            # End-of-speech when we have 400 ms silence
            if in_speech and sum(voiced_flag) == 0 and len(voiced_flag) == voiced_flag.maxlen:
                # Transcribe accumulated audio with performance tracking
                waveform = np.concatenate(samples)
                duration_sec = waveform.shape[0] / 16000

                # Apply advanced audio preprocessing
                processed_waveform = _preprocess_audio(waveform)

                # Track transcription performance
                import time
                transcription_start = time.time()

                segments, _ = _fw_model.transcribe(
                    processed_waveform,
                    language="en",
                    beam_size=1,  # Optimized for speed
                    temperature=0.0,  # Deterministic output
                    word_timestamps=False,
                    vad_filter=True,  # Enable VAD filtering
                    vad_parameters={
                        "threshold": 0.5,
                        "min_speech_duration_ms": 250,
                        "max_speech_duration_s": 30,
                        "min_silence_duration_ms": 2000,
                        "window_size_samples": 1024,
                        "speech_pad_ms": 400
                    }
                )

                transcription_time = time.time() - transcription_start
                rtf = transcription_time / duration_sec if duration_sec > 0 else 0

                # Update performance statistics
                _performance_stats["total_transcriptions"] += 1
                _performance_stats["total_processing_time"] += transcription_time
                _performance_stats["average_rtf"] = (
                    (_performance_stats["average_rtf"] * (_performance_stats["total_transcriptions"] - 1) + rtf) /
                    _performance_stats["total_transcriptions"]
                )

                text_parts = [seg.text.strip() for seg in segments]
                text = " ".join(text_parts)

                # Log performance for monitoring
                if _performance_stats["total_transcriptions"] % 10 == 0:  # Log every 10 transcriptions
                    _logger.debug(
                        f"fast_stt_patch: Transcription #{_performance_stats['total_transcriptions']} - "
                        f"Duration: {duration_sec:.2f}s, Processing: {transcription_time:.3f}s, RTF: {rtf:.3f}, "
                        f"Avg RTF: {_performance_stats['average_rtf']:.3f}"
                    )

                if text:
                    ev = SpeechEvent(
                        type=SpeechEventType.FINAL_TRANSCRIPT,
                        alternatives=[
                            SpeechEvent.SpeechData(
                                language="en",
                                text=text,
                                start_time=0.0,
                                end_time=duration_sec,
                                confidence=0.0,
                            )
                        ],
                    )
                    yield ev

                yield SpeechEvent(type=SpeechEventType.END_OF_SPEECH)
                # Reset buffers
                samples.clear()
                voiced_flag.clear()
                in_speech = False
                segment_start_ts = None

        # flush any remaining speech at stream end with optimization
        if in_speech and samples:
            waveform = np.concatenate(samples)

            # Apply preprocessing to final segment
            processed_waveform = _preprocess_audio(waveform)

            # Track final transcription performance
            import time
            transcription_start = time.time()

            segments, _ = _fw_model.transcribe(
                processed_waveform,
                language="en",
                beam_size=1,
                temperature=0.0,
                word_timestamps=False,
                vad_filter=True
            )

            transcription_time = time.time() - transcription_start
            duration_sec = len(processed_waveform) / 16000
            rtf = transcription_time / duration_sec if duration_sec > 0 else 0

            # Update final performance statistics
            _performance_stats["total_transcriptions"] += 1
            _performance_stats["total_processing_time"] += transcription_time
            _performance_stats["average_rtf"] = (
                (_performance_stats["average_rtf"] * (_performance_stats["total_transcriptions"] - 1) + rtf) /
                _performance_stats["total_transcriptions"]
            )

            text = " ".join(seg.text.strip() for seg in segments)
            if text:
                ev = SpeechEvent(
                    type=SpeechEventType.FINAL_TRANSCRIPT,
                    alternatives=[
                        SpeechEvent.SpeechData(
                            language="en",
                            text=text,
                            start_time=0.0,
                            end_time=calculate_audio_duration(waveform)  # type: ignore[arg-type]
                        )
                    ],
                )
                yield ev

    # ---------------------------------------------------------------------
    # Apply monkey-patch
    # ---------------------------------------------------------------------

    Agent.default.stt_node = staticmethod(_fast_stt_node)  # type: ignore[attr-defined]
    _logger.info("fast_stt_patch: Agent.default.stt_node patched with faster-whisper implementation") 