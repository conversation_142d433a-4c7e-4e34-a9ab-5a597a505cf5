#!/usr/bin/env python3
"""
UNIFIED CONFIGURATION SYSTEM
Consolidate all scattered configuration files into a single, validated system
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from enum import Enum
import yaml

logger = logging.getLogger(__name__)

class AudioBackend(Enum):
    """Available audio backends."""
    PYGAME = "pygame"
    SIMPLEAUDIO = "simpleaudio"
    SOUNDDEVICE = "sounddevice"
    AUTO = "auto"

class STTProvider(Enum):
    """Available STT providers."""
    FASTER_WHISPER = "faster_whisper"
    OPENAI_WHISPER = "openai_whisper"
    AZURE = "azure"

class TTSProvider(Enum):
    """Available TTS providers."""
    EDGE_TTS = "edge_tts"
    CHAT_TTS = "chat_tts"
    WINDOWS_SAPI = "windows_sapi"
    AZURE = "azure"

class LLMProvider(Enum):
    """Available LLM providers."""
    OLLAMA = "ollama"
    OPENAI = "openai"
    AZURE = "azure"

@dataclass
class PerformanceConfig:
    """Performance optimization settings."""
    # Memory management
    max_chat_history: int = 32
    aggressive_cleanup_threshold: int = 24
    memory_warning_threshold: float = 75.0
    memory_critical_threshold: float = 85.0
    
    # Audio processing
    audio_buffer_size: int = 512
    audio_sample_rate: int = 16000
    max_audio_buffers: int = 50
    
    # STT optimization
    stt_rtf_target: float = 0.25
    stt_model_cache_size: int = 3
    stt_cpu_threads: int = 4
    
    # TTS optimization
    tts_first_frame_target_ms: float = 100.0
    tts_streaming_enabled: bool = True
    tts_cache_enabled: bool = True
    
    # LLM optimization
    llm_response_timeout: float = 30.0
    llm_max_tokens: int = 2048
    
    # End-to-end targets
    e2e_total_target: float = 2.0

@dataclass
class AudioConfig:
    """Audio processing configuration."""
    backend: AudioBackend = AudioBackend.AUTO
    sample_rate: int = 16000
    channels: int = 1
    buffer_size: int = 512
    
    # VAD settings
    vad_mode: str = "webrtc"  # webrtc, silero, energy
    vad_aggressiveness: int = 2
    
    # Audio quality
    bit_depth: int = 16
    format: str = "PCM"

@dataclass
class STTConfig:
    """Speech-to-Text configuration."""
    provider: STTProvider = STTProvider.FASTER_WHISPER
    model: str = "tiny.en"
    device: str = "auto"  # auto, cpu, cuda
    compute_type: str = "int8"
    language: str = "en"
    
    # Performance settings
    beam_size: int = 1
    best_of: int = 1
    temperature: float = 0.0
    
    # Advanced settings
    vad_filter: bool = True
    vad_parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.vad_parameters is None:
            self.vad_parameters = {
                "threshold": 0.5,
                "min_speech_duration_ms": 250,
                "max_speech_duration_s": 30,
                "min_silence_duration_ms": 2000,
                "window_size_samples": 1024,
                "speech_pad_ms": 400
            }

@dataclass
class TTSConfig:
    """Text-to-Speech configuration."""
    provider: TTSProvider = TTSProvider.EDGE_TTS
    voice: str = "en-US-AriaNeural"
    rate: str = "+0%"
    pitch: str = "+0Hz"
    
    # Quality settings
    quality: str = "high"
    format: str = "audio-24khz-48kbitrate-mono-mp3"
    
    # Streaming settings
    streaming: bool = True
    chunk_size: int = 1024

@dataclass
class LLMConfig:
    """Large Language Model configuration."""
    provider: LLMProvider = LLMProvider.OLLAMA
    model: str = "llama3.2:1b"
    base_url: str = "http://localhost:11434"
    
    # Generation settings
    temperature: float = 0.7
    max_tokens: int = 2048
    top_p: float = 0.9
    top_k: int = 40
    
    # System settings
    system_prompt: str = "You are a helpful AI assistant."
    timeout: float = 30.0

@dataclass
class MonitoringConfig:
    """System monitoring configuration."""
    enabled: bool = True
    interval_seconds: int = 30
    
    # Metrics collection
    collect_memory: bool = True
    collect_cpu: bool = True
    collect_disk: bool = True
    collect_performance: bool = True
    
    # Alerting
    alert_memory_threshold: float = 85.0
    alert_disk_threshold: float = 90.0
    alert_response_time_threshold: float = 5.0
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = "voice_ai.log"
    log_rotation: bool = True
    max_log_size_mb: int = 100

@dataclass
class UnifiedConfig:
    """Unified configuration for the entire voice AI system."""
    performance: PerformanceConfig = None
    audio: AudioConfig = None
    stt: STTConfig = None
    tts: TTSConfig = None
    llm: LLMConfig = None
    monitoring: MonitoringConfig = None
    
    # Environment settings
    environment: str = "development"  # development, production, testing
    debug: bool = False
    
    def __post_init__(self):
        if self.performance is None:
            self.performance = PerformanceConfig()
        if self.audio is None:
            self.audio = AudioConfig()
        if self.stt is None:
            self.stt = STTConfig()
        if self.tts is None:
            self.tts = TTSConfig()
        if self.llm is None:
            self.llm = LLMConfig()
        if self.monitoring is None:
            self.monitoring = MonitoringConfig()

class ConfigManager:
    """Unified configuration manager."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = Path(config_path) if config_path else Path("unified_config.yaml")
        self.config: Optional[UnifiedConfig] = None
        self._legacy_configs = []
        
    def load_config(self) -> UnifiedConfig:
        """Load configuration from file or create default."""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    if self.config_path.suffix.lower() == '.yaml':
                        data = yaml.safe_load(f)
                    else:
                        data = json.load(f)
                
                self.config = self._dict_to_config(data)
                logger.info(f"Configuration loaded from {self.config_path}")
                
            except Exception as e:
                logger.error(f"Failed to load config from {self.config_path}: {e}")
                self.config = UnifiedConfig()
        else:
            logger.info("No config file found, using defaults")
            self.config = UnifiedConfig()
        
        # Apply environment overrides
        self._apply_environment_overrides()
        
        # Validate configuration
        self._validate_config()
        
        return self.config
    
    def save_config(self, config: Optional[UnifiedConfig] = None) -> None:
        """Save configuration to file."""
        if config:
            self.config = config
        
        if not self.config:
            raise ValueError("No configuration to save")
        
        try:
            config_dict = self._config_to_dict(self.config)
            
            with open(self.config_path, 'w') as f:
                if self.config_path.suffix.lower() == '.yaml':
                    yaml.dump(config_dict, f, default_flow_style=False, indent=2)
                else:
                    json.dump(config_dict, f, indent=2)
            
            logger.info(f"Configuration saved to {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to save config to {self.config_path}: {e}")
            raise
    
    def migrate_legacy_configs(self) -> None:
        """Migrate from legacy configuration files."""
        logger.info("Migrating legacy configuration files...")
        
        # Find all legacy config files
        models_dir = Path("models")
        if models_dir.exists():
            legacy_files = list(models_dir.glob("*.json")) + list(models_dir.glob("*.yaml"))
            
            for legacy_file in legacy_files:
                try:
                    self._migrate_legacy_file(legacy_file)
                    self._legacy_configs.append(str(legacy_file))
                except Exception as e:
                    logger.warning(f"Failed to migrate {legacy_file}: {e}")
        
        # Save migrated configuration
        if self._legacy_configs:
            self.save_config()
            logger.info(f"Migrated {len(self._legacy_configs)} legacy config files")
    
    def _migrate_legacy_file(self, file_path: Path) -> None:
        """Migrate a single legacy config file."""
        try:
            with open(file_path, 'r') as f:
                if file_path.suffix.lower() == '.yaml':
                    data = yaml.safe_load(f)
                else:
                    data = json.load(f)
            
            # Map legacy settings to new structure
            if "performance" in file_path.name.lower():
                self._migrate_performance_config(data)
            elif "audio" in file_path.name.lower():
                self._migrate_audio_config(data)
            elif "stt" in file_path.name.lower() or "whisper" in file_path.name.lower():
                self._migrate_stt_config(data)
            elif "tts" in file_path.name.lower():
                self._migrate_tts_config(data)
            elif "llm" in file_path.name.lower() or "ollama" in file_path.name.lower():
                self._migrate_llm_config(data)
            
        except Exception as e:
            logger.warning(f"Failed to migrate {file_path}: {e}")
    
    def _migrate_performance_config(self, data: Dict[str, Any]) -> None:
        """Migrate performance configuration."""
        if not self.config:
            self.config = UnifiedConfig()
        
        # Map known performance settings
        mapping = {
            "max_chat_history": "max_chat_history",
            "memory_threshold": "memory_warning_threshold",
            "rtf_target": "stt_rtf_target",
            "first_frame_target": "tts_first_frame_target_ms"
        }
        
        for old_key, new_key in mapping.items():
            if old_key in data:
                setattr(self.config.performance, new_key, data[old_key])
    
    def _migrate_audio_config(self, data: Dict[str, Any]) -> None:
        """Migrate audio configuration."""
        if not self.config:
            self.config = UnifiedConfig()
        
        mapping = {
            "sample_rate": "sample_rate",
            "channels": "channels",
            "buffer_size": "buffer_size",
            "backend": "backend"
        }
        
        for old_key, new_key in mapping.items():
            if old_key in data:
                setattr(self.config.audio, new_key, data[old_key])
    
    def _migrate_stt_config(self, data: Dict[str, Any]) -> None:
        """Migrate STT configuration."""
        if not self.config:
            self.config = UnifiedConfig()
        
        mapping = {
            "model": "model",
            "device": "device",
            "compute_type": "compute_type",
            "language": "language"
        }
        
        for old_key, new_key in mapping.items():
            if old_key in data:
                setattr(self.config.stt, new_key, data[old_key])
    
    def _migrate_tts_config(self, data: Dict[str, Any]) -> None:
        """Migrate TTS configuration."""
        if not self.config:
            self.config = UnifiedConfig()
        
        mapping = {
            "voice": "voice",
            "rate": "rate",
            "pitch": "pitch",
            "quality": "quality"
        }
        
        for old_key, new_key in mapping.items():
            if old_key in data:
                setattr(self.config.tts, new_key, data[old_key])
    
    def _migrate_llm_config(self, data: Dict[str, Any]) -> None:
        """Migrate LLM configuration."""
        if not self.config:
            self.config = UnifiedConfig()
        
        mapping = {
            "model": "model",
            "base_url": "base_url",
            "temperature": "temperature",
            "max_tokens": "max_tokens"
        }
        
        for old_key, new_key in mapping.items():
            if old_key in data:
                setattr(self.config.llm, new_key, data[old_key])
    
    def _apply_environment_overrides(self) -> None:
        """Apply environment variable overrides."""
        if not self.config:
            return
        
        # Environment variable mappings
        env_mappings = {
            "VOICE_AI_DEBUG": ("debug", bool),
            "VOICE_AI_ENVIRONMENT": ("environment", str),
            "VOICE_AI_STT_MODEL": ("stt.model", str),
            "VOICE_AI_TTS_VOICE": ("tts.voice", str),
            "VOICE_AI_LLM_MODEL": ("llm.model", str),
            "VOICE_AI_MEMORY_THRESHOLD": ("performance.memory_warning_threshold", float),
        }
        
        for env_var, (config_path, config_type) in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                try:
                    # Convert value to appropriate type
                    if config_type == bool:
                        value = env_value.lower() in ('true', '1', 'yes', 'on')
                    elif config_type == float:
                        value = float(env_value)
                    elif config_type == int:
                        value = int(env_value)
                    else:
                        value = env_value
                    
                    # Set nested attribute
                    self._set_nested_attr(self.config, config_path, value)
                    logger.info(f"Applied environment override: {env_var}={value}")
                    
                except Exception as e:
                    logger.warning(f"Failed to apply environment override {env_var}: {e}")
    
    def _set_nested_attr(self, obj: Any, path: str, value: Any) -> None:
        """Set nested attribute using dot notation."""
        parts = path.split('.')
        for part in parts[:-1]:
            obj = getattr(obj, part)
        setattr(obj, parts[-1], value)
    
    def _validate_config(self) -> None:
        """Validate configuration values."""
        if not self.config:
            return
        
        # Validate performance settings
        if self.config.performance.max_chat_history < 1:
            logger.warning("max_chat_history must be >= 1, setting to 1")
            self.config.performance.max_chat_history = 1
        
        if self.config.performance.stt_rtf_target <= 0:
            logger.warning("stt_rtf_target must be > 0, setting to 0.25")
            self.config.performance.stt_rtf_target = 0.25
        
        # Validate audio settings
        if self.config.audio.sample_rate not in [8000, 16000, 22050, 44100, 48000]:
            logger.warning(f"Unusual sample rate: {self.config.audio.sample_rate}")
        
        # Validate paths and URLs
        if self.config.llm.provider == LLMProvider.OLLAMA:
            if not self.config.llm.base_url.startswith(('http://', 'https://')):
                logger.warning(f"Invalid Ollama base URL: {self.config.llm.base_url}")
    
    def _dict_to_config(self, data: Dict[str, Any]) -> UnifiedConfig:
        """Convert dictionary to UnifiedConfig."""
        # This is a simplified conversion - in practice, you'd want more robust handling
        config = UnifiedConfig()
        
        if "performance" in data:
            config.performance = PerformanceConfig(**data["performance"])
        if "audio" in data:
            config.audio = AudioConfig(**data["audio"])
        if "stt" in data:
            config.stt = STTConfig(**data["stt"])
        if "tts" in data:
            config.tts = TTSConfig(**data["tts"])
        if "llm" in data:
            config.llm = LLMConfig(**data["llm"])
        if "monitoring" in data:
            config.monitoring = MonitoringConfig(**data["monitoring"])
        
        # Top-level settings
        if "environment" in data:
            config.environment = data["environment"]
        if "debug" in data:
            config.debug = data["debug"]
        
        return config
    
    def _config_to_dict(self, config: UnifiedConfig) -> Dict[str, Any]:
        """Convert UnifiedConfig to dictionary."""
        return {
            "performance": asdict(config.performance),
            "audio": asdict(config.audio),
            "stt": asdict(config.stt),
            "tts": asdict(config.tts),
            "llm": asdict(config.llm),
            "monitoring": asdict(config.monitoring),
            "environment": config.environment,
            "debug": config.debug
        }
    
    def get_legacy_configs(self) -> List[str]:
        """Get list of migrated legacy config files."""
        return self._legacy_configs.copy()


# Global configuration instance
_config_manager: Optional[ConfigManager] = None

def get_config() -> UnifiedConfig:
    """Get the global configuration instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
        _config_manager.load_config()
    return _config_manager.config

def reload_config() -> UnifiedConfig:
    """Reload configuration from file."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager.load_config()

def save_config(config: Optional[UnifiedConfig] = None) -> None:
    """Save configuration to file."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    _config_manager.save_config(config)

def migrate_legacy_configs() -> None:
    """Migrate legacy configuration files."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
        _config_manager.load_config()
    _config_manager.migrate_legacy_configs()


if __name__ == "__main__":
    # Example usage
    logging.basicConfig(level=logging.INFO)
    
    # Create config manager
    manager = ConfigManager()
    
    # Migrate legacy configs
    manager.migrate_legacy_configs()
    
    # Load configuration
    config = manager.load_config()
    
    # Print configuration summary
    print("🔧 UNIFIED CONFIGURATION LOADED")
    print(f"Environment: {config.environment}")
    print(f"Debug: {config.debug}")
    print(f"STT Model: {config.stt.model}")
    print(f"TTS Voice: {config.tts.voice}")
    print(f"LLM Model: {config.llm.model}")
    print(f"Memory Threshold: {config.performance.memory_warning_threshold}%")
    
    # Save unified config
    manager.save_config()
    
    print(f"✅ Configuration saved to {manager.config_path}")
    
    legacy_configs = manager.get_legacy_configs()
    if legacy_configs:
        print(f"📁 Migrated {len(legacy_configs)} legacy config files")
