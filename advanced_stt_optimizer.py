#!/usr/bin/env python3
"""
ADVANCED STT PERFORMANCE OPTIMIZER
Ultra-high performance Speech-to-Text with GPU acceleration, model caching, and streaming optimizations
"""

import asyncio
import logging
import time
import threading
from typing import Dict, Any, Optional, List, Tuple, AsyncGenerator
from dataclasses import dataclass
from enum import Enum
import numpy as np
from collections import deque
import weakref
import gc

logger = logging.getLogger(__name__)

class STTDevice(Enum):
    """STT processing devices."""
    CPU = "cpu"
    CUDA = "cuda"
    AUTO = "auto"

class STTModel(Enum):
    """Available STT models with performance characteristics."""
    TINY = "tiny"           # ~39MB, RTF ~0.1, accuracy 85%
    TINY_EN = "tiny.en"     # ~39MB, RTF ~0.08, accuracy 87%
    BASE = "base"           # ~74MB, RTF ~0.2, accuracy 89%
    BASE_EN = "base.en"     # ~74MB, RTF ~0.18, accuracy 91%
    SMALL = "small"         # ~244MB, RTF ~0.4, accuracy 93%
    SMALL_EN = "small.en"   # ~244MB, RTF ~0.35, accuracy 94%

@dataclass
class STTPerformanceConfig:
    """Advanced STT performance configuration."""
    # Model selection
    model: STTModel = STTModel.TINY_EN
    device: STTDevice = STTDevice.AUTO
    compute_type: str = "int8"  # int8, float16, float32
    
    # Performance optimization
    cpu_threads: int = 4
    num_workers: int = 1
    beam_size: int = 1  # Lower beam size for speed
    best_of: int = 1    # Lower best_of for speed
    temperature: float = 0.0  # Deterministic output
    
    # Streaming optimization
    chunk_length_s: float = 30.0  # Maximum chunk length
    vad_filter: bool = True
    vad_threshold: float = 0.5
    
    # Caching
    enable_model_cache: bool = True
    cache_size: int = 3
    cache_cleanup_interval: int = 300  # 5 minutes
    
    # GPU optimization
    gpu_memory_fraction: float = 0.8
    enable_mixed_precision: bool = True
    
    # Audio preprocessing
    enable_audio_enhancement: bool = True
    noise_reduction: bool = True
    normalize_audio: bool = True

class ModelCache:
    """Advanced model caching system with LRU eviction."""
    
    def __init__(self, max_size: int = 3):
        self.max_size = max_size
        self.cache: Dict[str, Any] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        
    def get(self, key: str) -> Optional[Any]:
        """Get model from cache."""
        with self.lock:
            if key in self.cache:
                self.access_times[key] = time.time()
                return self.cache[key]
            return None
    
    def put(self, key: str, model: Any) -> None:
        """Put model in cache with LRU eviction."""
        with self.lock:
            # If cache is full, evict least recently used
            if len(self.cache) >= self.max_size and key not in self.cache:
                lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
                self._evict(lru_key)
            
            self.cache[key] = model
            self.access_times[key] = time.time()
    
    def _evict(self, key: str) -> None:
        """Evict model from cache."""
        if key in self.cache:
            del self.cache[key]
            del self.access_times[key]
            gc.collect()  # Force garbage collection
            logger.debug(f"Evicted model from cache: {key}")
    
    def clear(self) -> None:
        """Clear entire cache."""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            gc.collect()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "keys": list(self.cache.keys()),
                "hit_rate": getattr(self, '_hit_count', 0) / max(getattr(self, '_total_requests', 1), 1)
            }

class AudioPreprocessor:
    """Advanced audio preprocessing for optimal STT performance."""
    
    def __init__(self, config: STTPerformanceConfig):
        self.config = config
        self.sample_rate = 16000  # Whisper's expected sample rate
        
    def preprocess(self, audio: np.ndarray) -> np.ndarray:
        """Apply comprehensive audio preprocessing."""
        if not self.config.enable_audio_enhancement:
            return audio
        
        # Ensure float32 format
        if audio.dtype != np.float32:
            audio = audio.astype(np.float32)
        
        # Normalize audio levels
        if self.config.normalize_audio:
            audio = self._normalize_audio(audio)
        
        # Noise reduction
        if self.config.noise_reduction:
            audio = self._reduce_noise(audio)
        
        # Pre-emphasis filter for speech clarity
        audio = self._apply_preemphasis(audio)
        
        # High-pass filter to remove low-frequency noise
        audio = self._high_pass_filter(audio)
        
        return audio
    
    def _normalize_audio(self, audio: np.ndarray) -> np.ndarray:
        """Normalize audio to optimal levels."""
        # RMS normalization
        rms = np.sqrt(np.mean(audio**2))
        if rms > 0:
            target_rms = 0.1
            audio = audio * (target_rms / rms)
        
        # Peak normalization (prevent clipping)
        peak = np.max(np.abs(audio))
        if peak > 0.95:
            audio = audio * (0.95 / peak)
        
        return audio
    
    def _reduce_noise(self, audio: np.ndarray) -> np.ndarray:
        """Simple spectral subtraction noise reduction."""
        try:
            # Estimate noise from first 0.5 seconds
            noise_samples = min(int(0.5 * self.sample_rate), len(audio) // 4)
            noise_profile = np.mean(audio[:noise_samples]**2)
            
            # Apply noise gate
            threshold = noise_profile * 2
            mask = audio**2 > threshold
            audio = audio * mask
            
            return audio
        except Exception:
            return audio
    
    def _apply_preemphasis(self, audio: np.ndarray, alpha: float = 0.97) -> np.ndarray:
        """Apply pre-emphasis filter for speech enhancement."""
        if len(audio) < 2:
            return audio
        
        emphasized = np.zeros_like(audio)
        emphasized[0] = audio[0]
        emphasized[1:] = audio[1:] - alpha * audio[:-1]
        
        return emphasized
    
    def _high_pass_filter(self, audio: np.ndarray, cutoff: float = 80.0) -> np.ndarray:
        """Apply high-pass filter to remove low-frequency noise."""
        try:
            from scipy import signal
            
            # Design high-pass filter
            nyquist = self.sample_rate / 2
            normalized_cutoff = cutoff / nyquist
            b, a = signal.butter(4, normalized_cutoff, btype='high')
            
            # Apply filter
            filtered = signal.filtfilt(b, a, audio)
            return filtered.astype(np.float32)
            
        except ImportError:
            # Fallback: simple high-pass using difference
            if len(audio) > 1:
                audio[1:] = audio[1:] - 0.95 * audio[:-1]
            return audio

class AdvancedSTTEngine:
    """Ultra-high performance STT engine with all optimizations."""
    
    def __init__(self, config: STTPerformanceConfig = None):
        self.config = config or STTPerformanceConfig()
        self.model_cache = ModelCache(self.config.cache_size)
        self.preprocessor = AudioPreprocessor(self.config)
        
        # Performance tracking
        self.stats = {
            "total_requests": 0,
            "total_processing_time": 0.0,
            "average_rtf": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
            "gpu_utilization": 0.0
        }
        
        # Current model instance
        self._current_model = None
        self._current_model_key = None
        self._model_lock = threading.RLock()
        
        # Background cleanup task
        self._cleanup_task = None
        self._running = False
        
        logger.info("Advanced STT Engine initialized")
    
    async def initialize(self) -> bool:
        """Initialize the STT engine."""
        try:
            logger.info("🎤 Initializing Advanced STT Engine...")
            
            # Load initial model
            await self._load_model()
            
            # Start background cleanup
            self._running = True
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            logger.info("✅ Advanced STT Engine ready")
            return True
            
        except Exception as e:
            logger.error(f"❌ STT Engine initialization failed: {e}")
            return False
    
    async def _load_model(self) -> Any:
        """Load STT model with caching and optimization."""
        model_key = f"{self.config.model.value}_{self.config.device.value}_{self.config.compute_type}"
        
        with self._model_lock:
            # Check cache first
            cached_model = self.model_cache.get(model_key)
            if cached_model is not None:
                self.stats["cache_hits"] += 1
                self._current_model = cached_model
                self._current_model_key = model_key
                logger.debug(f"Using cached model: {model_key}")
                return cached_model
            
            self.stats["cache_misses"] += 1
            
            # Determine device
            device = self._get_optimal_device()
            compute_type = self._get_optimal_compute_type(device)
            
            logger.info(f"Loading model: {self.config.model.value} on {device} with {compute_type}")
            
            # Load model with optimizations
            try:
                from faster_whisper import WhisperModel
                
                model = WhisperModel(
                    self.config.model.value,
                    device=device,
                    compute_type=compute_type,
                    cpu_threads=self.config.cpu_threads,
                    num_workers=self.config.num_workers,
                    download_root=None,  # Use default cache
                    local_files_only=False
                )
                
                # Warm up the model
                await self._warm_up_model(model)
                
                # Cache the model
                self.model_cache.put(model_key, model)
                self._current_model = model
                self._current_model_key = model_key
                
                logger.info(f"✅ Model loaded and cached: {model_key}")
                return model
                
            except Exception as e:
                logger.error(f"Failed to load model {self.config.model.value}: {e}")
                raise
    
    def _get_optimal_device(self) -> str:
        """Determine optimal device for processing."""
        if self.config.device == STTDevice.AUTO:
            try:
                import torch
                if torch.cuda.is_available():
                    # Check GPU memory
                    gpu_memory = torch.cuda.get_device_properties(0).total_memory
                    if gpu_memory > 2 * 1024**3:  # 2GB minimum
                        return "cuda"
                    else:
                        logger.warning("GPU memory insufficient, falling back to CPU")
                        return "cpu"
                else:
                    return "cpu"
            except ImportError:
                return "cpu"
        else:
            return self.config.device.value
    
    def _get_optimal_compute_type(self, device: str) -> str:
        """Determine optimal compute type for device."""
        if device == "cuda":
            if self.config.enable_mixed_precision:
                return "float16"
            else:
                return "float32"
        else:
            return "int8"  # CPU optimization
    
    async def _warm_up_model(self, model: Any) -> None:
        """Warm up model with dummy audio."""
        try:
            # Generate 1 second of silence for warmup
            dummy_audio = np.zeros(16000, dtype=np.float32)
            
            # Run transcription to warm up
            segments, _ = model.transcribe(
                dummy_audio,
                language="en",
                beam_size=1,
                temperature=0.0,
                word_timestamps=False
            )
            
            # Consume generator
            list(segments)
            
            logger.debug("Model warmed up successfully")
            
        except Exception as e:
            logger.warning(f"Model warmup failed: {e}")
    
    async def transcribe(self, audio: np.ndarray, language: str = "en") -> Tuple[str, float]:
        """Transcribe audio with maximum performance."""
        start_time = time.time()
        
        try:
            # Ensure model is loaded
            if self._current_model is None:
                await self._load_model()
            
            # Preprocess audio
            processed_audio = self.preprocessor.preprocess(audio)
            
            # Transcribe
            segments, info = self._current_model.transcribe(
                processed_audio,
                language=language,
                beam_size=self.config.beam_size,
                best_of=self.config.best_of,
                temperature=self.config.temperature,
                vad_filter=self.config.vad_filter,
                vad_parameters={
                    "threshold": self.config.vad_threshold,
                    "min_speech_duration_ms": 250,
                    "max_speech_duration_s": 30,
                    "min_silence_duration_ms": 2000,
                    "window_size_samples": 1024,
                    "speech_pad_ms": 400
                } if self.config.vad_filter else None,
                word_timestamps=False,
                chunk_length=self.config.chunk_length_s
            )
            
            # Extract text
            text_parts = [segment.text.strip() for segment in segments]
            text = " ".join(text_parts).strip()
            
            # Calculate performance metrics
            processing_time = time.time() - start_time
            audio_duration = len(processed_audio) / 16000
            rtf = processing_time / audio_duration if audio_duration > 0 else 0
            
            # Update statistics
            self._update_stats(processing_time, rtf)
            
            logger.debug(f"Transcribed {audio_duration:.2f}s audio in {processing_time:.3f}s (RTF: {rtf:.3f})")
            
            return text, rtf
            
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return "", 0.0
    
    def _update_stats(self, processing_time: float, rtf: float) -> None:
        """Update performance statistics."""
        self.stats["total_requests"] += 1
        self.stats["total_processing_time"] += processing_time
        
        # Calculate running average RTF
        if self.stats["total_requests"] > 0:
            self.stats["average_rtf"] = (
                (self.stats["average_rtf"] * (self.stats["total_requests"] - 1) + rtf) /
                self.stats["total_requests"]
            )
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop."""
        while self._running:
            try:
                await asyncio.sleep(self.config.cache_cleanup_interval)
                
                # Cleanup old cache entries if needed
                cache_stats = self.model_cache.get_stats()
                if cache_stats["size"] >= self.model_cache.max_size:
                    logger.debug("Running model cache cleanup")
                
                # Force garbage collection periodically
                gc.collect()
                
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
                await asyncio.sleep(60)
    
    async def shutdown(self) -> None:
        """Shutdown the STT engine."""
        logger.info("🛑 Shutting down Advanced STT Engine...")
        
        self._running = False
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        # Clear cache
        self.model_cache.clear()
        
        logger.info("✅ Advanced STT Engine shutdown complete")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        cache_stats = self.model_cache.get_stats()
        
        return {
            "transcription_stats": self.stats.copy(),
            "cache_stats": cache_stats,
            "config": {
                "model": self.config.model.value,
                "device": self.config.device.value,
                "compute_type": self.config.compute_type,
                "beam_size": self.config.beam_size
            },
            "current_model": self._current_model_key
        }
    
    def optimize_for_speed(self) -> None:
        """Optimize configuration for maximum speed."""
        self.config.model = STTModel.TINY_EN
        self.config.beam_size = 1
        self.config.best_of = 1
        self.config.temperature = 0.0
        self.config.vad_filter = True
        self.config.chunk_length_s = 30.0
        
        logger.info("🚀 STT optimized for maximum speed")
    
    def optimize_for_accuracy(self) -> None:
        """Optimize configuration for maximum accuracy."""
        self.config.model = STTModel.SMALL_EN
        self.config.beam_size = 5
        self.config.best_of = 5
        self.config.temperature = 0.0
        self.config.vad_filter = True
        
        logger.info("🎯 STT optimized for maximum accuracy")
    
    def optimize_for_balance(self) -> None:
        """Optimize configuration for balanced speed/accuracy."""
        self.config.model = STTModel.BASE_EN
        self.config.beam_size = 2
        self.config.best_of = 2
        self.config.temperature = 0.0
        self.config.vad_filter = True
        
        logger.info("⚖️ STT optimized for balanced performance")


async def main():
    """Test the advanced STT optimizer."""
    logging.basicConfig(level=logging.INFO)
    
    logger.info("🎤 TESTING ADVANCED STT OPTIMIZER")
    
    try:
        # Create STT engine with speed optimization
        config = STTPerformanceConfig()
        engine = AdvancedSTTEngine(config)
        
        # Optimize for speed
        engine.optimize_for_speed()
        
        # Initialize
        success = await engine.initialize()
        if not success:
            logger.error("Failed to initialize STT engine")
            return 1
        
        # Test transcription
        logger.info("Testing transcription performance...")
        
        # Generate test audio (1 second of sine wave)
        sample_rate = 16000
        duration = 1.0
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        test_audio = np.sin(440 * 2 * np.pi * t).astype(np.float32) * 0.1
        
        # Run multiple tests
        rtf_values = []
        for i in range(5):
            text, rtf = await engine.transcribe(test_audio)
            rtf_values.append(rtf)
            logger.info(f"Test {i+1}: RTF={rtf:.3f}, Text='{text}'")
        
        # Get performance stats
        stats = engine.get_performance_stats()
        
        print("\n" + "="*50)
        print("🎤 ADVANCED STT PERFORMANCE RESULTS")
        print("="*50)
        print(f"Model: {stats['config']['model']}")
        print(f"Device: {stats['config']['device']}")
        print(f"Average RTF: {stats['transcription_stats']['average_rtf']:.3f}")
        print(f"Cache Hit Rate: {stats['cache_stats']['hit_rate']:.2%}")
        print(f"Total Requests: {stats['transcription_stats']['total_requests']}")
        
        if rtf_values:
            avg_rtf = sum(rtf_values) / len(rtf_values)
            min_rtf = min(rtf_values)
            max_rtf = max(rtf_values)
            print(f"Test RTF - Avg: {avg_rtf:.3f}, Min: {min_rtf:.3f}, Max: {max_rtf:.3f}")
        
        print("="*50)
        
        # Shutdown
        await engine.shutdown()
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ STT optimization test failed: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
