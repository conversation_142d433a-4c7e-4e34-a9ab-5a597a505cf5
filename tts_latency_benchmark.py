#!/usr/bin/env python3
"""
TTS LATENCY BENCHMARK
Comprehensive benchmarking and optimization testing for Text-to-Speech latency
"""

import asyncio
import logging
import time
import json
import statistics
from typing import Dict, Any, List, Tuple
import tempfile
import os

# Import optimized components
from ultra_fast_tts_engine import UltraFastTTSEngine, TTSPerformanceConfig, TTSProvider, TTSQuality

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("tts_benchmark")

class TTSLatencyBenchmark:
    """Comprehensive TTS latency benchmarking system."""
    
    def __init__(self):
        self.results = {}
        
    async def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """Run complete TTS latency benchmark."""
        logger.info("🔊 STARTING COMPREHENSIVE TTS LATENCY BENCHMARK")
        logger.info("=" * 60)
        
        benchmark_start = time.time()
        
        # Test 1: First Frame Latency Analysis
        logger.info("⚡ Test 1: First Frame Latency Analysis")
        self.results["first_frame_latency"] = await self._benchmark_first_frame_latency()
        
        # Test 2: Provider Performance Comparison
        logger.info("📊 Test 2: Provider Performance Comparison")
        self.results["provider_comparison"] = await self._benchmark_providers()
        
        # Test 3: Text Length Impact
        logger.info("📏 Test 3: Text Length Impact on Latency")
        self.results["text_length_impact"] = await self._benchmark_text_lengths()
        
        # Test 4: Caching Performance
        logger.info("💾 Test 4: Caching Performance Analysis")
        self.results["caching_performance"] = await self._benchmark_caching()
        
        # Test 5: Streaming vs Non-Streaming
        logger.info("🌊 Test 5: Streaming vs Non-Streaming Performance")
        self.results["streaming_comparison"] = await self._benchmark_streaming()
        
        # Test 6: Quality vs Speed Trade-offs
        logger.info("⚖️ Test 6: Quality vs Speed Trade-offs")
        self.results["quality_speed_tradeoffs"] = await self._benchmark_quality_modes()
        
        # Test 7: Concurrent Synthesis
        logger.info("🔄 Test 7: Concurrent Synthesis Performance")
        self.results["concurrent_synthesis"] = await self._benchmark_concurrent()
        
        # Test 8: Real-world Scenario Testing
        logger.info("🌍 Test 8: Real-world Scenario Testing")
        self.results["realworld_scenarios"] = await self._benchmark_realworld()
        
        total_time = time.time() - benchmark_start
        self.results["benchmark_summary"] = {
            "total_time": total_time,
            "timestamp": time.time(),
            "tests_completed": 8
        }
        
        logger.info("=" * 60)
        logger.info(f"✅ BENCHMARK COMPLETE ({total_time:.1f}s)")
        
        return self.results
    
    async def _benchmark_first_frame_latency(self) -> Dict[str, Any]:
        """Benchmark first frame latency across different configurations."""
        results = {}
        
        test_phrases = [
            "Hello",
            "I understand",
            "Tell me more about that",
            "This is a longer sentence to test first frame latency with more content"
        ]
        
        # Test different configurations
        configs = {
            "speed_optimized": {
                "quality_mode": TTSQuality.SPEED,
                "enable_caching": True,
                "enable_pregeneration": True,
                "first_frame_target_ms": 50.0
            },
            "balanced": {
                "quality_mode": TTSQuality.BALANCED,
                "enable_caching": True,
                "enable_pregeneration": False,
                "first_frame_target_ms": 100.0
            },
            "quality_focused": {
                "quality_mode": TTSQuality.QUALITY,
                "enable_caching": False,
                "enable_pregeneration": False,
                "first_frame_target_ms": 200.0
            }
        }
        
        for config_name, config_params in configs.items():
            logger.info(f"  Testing {config_name} configuration")
            
            try:
                config = TTSPerformanceConfig()
                config.quality_mode = config_params["quality_mode"]
                config.enable_caching = config_params["enable_caching"]
                config.enable_pregeneration = config_params["enable_pregeneration"]
                config.first_frame_target_ms = config_params["first_frame_target_ms"]
                
                engine = UltraFastTTSEngine(config)
                await engine.initialize()
                
                first_frame_latencies = []
                
                for phrase in test_phrases:
                    start_time = time.time()
                    first_chunk_time = None
                    
                    async for chunk in engine.synthesize_streaming(phrase):
                        if first_chunk_time is None:
                            first_chunk_time = time.time()
                            break  # Only measure first chunk
                    
                    if first_chunk_time:
                        latency_ms = (first_chunk_time - start_time) * 1000
                        first_frame_latencies.append(latency_ms)
                
                await engine.shutdown()
                
                if first_frame_latencies:
                    results[config_name] = {
                        "avg_first_frame_ms": statistics.mean(first_frame_latencies),
                        "min_first_frame_ms": min(first_frame_latencies),
                        "max_first_frame_ms": max(first_frame_latencies),
                        "std_first_frame_ms": statistics.stdev(first_frame_latencies) if len(first_frame_latencies) > 1 else 0,
                        "target_met": statistics.mean(first_frame_latencies) < config_params["first_frame_target_ms"],
                        "samples": len(first_frame_latencies)
                    }
                    
                    logger.info(f"    Avg first frame: {results[config_name]['avg_first_frame_ms']:.1f}ms")
                
            except Exception as e:
                logger.error(f"    Configuration {config_name} failed: {e}")
                results[config_name] = {"error": str(e)}
        
        return results
    
    async def _benchmark_providers(self) -> Dict[str, Any]:
        """Benchmark different TTS providers."""
        results = {}
        test_text = "This is a test of the TTS provider performance."
        
        providers_to_test = [TTSProvider.EDGE_TTS, TTSProvider.PYTTSX3]
        
        for provider in providers_to_test:
            logger.info(f"  Testing provider: {provider.value}")
            
            try:
                config = TTSPerformanceConfig()
                config.primary_provider = provider
                config.enable_caching = False  # Test raw performance
                
                engine = UltraFastTTSEngine(config)
                await engine.initialize()
                
                latencies = []
                first_frame_latencies = []
                
                for i in range(5):  # 5 tests per provider
                    start_time = time.time()
                    first_chunk_time = None
                    chunk_count = 0
                    
                    async for chunk in engine.synthesize_streaming(test_text):
                        if first_chunk_time is None:
                            first_chunk_time = time.time()
                        chunk_count += 1
                    
                    total_latency = (time.time() - start_time) * 1000
                    first_frame_latency = (first_chunk_time - start_time) * 1000 if first_chunk_time else 0
                    
                    latencies.append(total_latency)
                    first_frame_latencies.append(first_frame_latency)
                
                await engine.shutdown()
                
                results[provider.value] = {
                    "avg_total_latency_ms": statistics.mean(latencies),
                    "avg_first_frame_ms": statistics.mean(first_frame_latencies),
                    "min_first_frame_ms": min(first_frame_latencies),
                    "max_first_frame_ms": max(first_frame_latencies),
                    "tests_run": len(latencies)
                }
                
                logger.info(f"    Avg first frame: {results[provider.value]['avg_first_frame_ms']:.1f}ms")
                
            except Exception as e:
                logger.error(f"    Provider {provider.value} test failed: {e}")
                results[provider.value] = {"error": str(e)}
        
        return results
    
    async def _benchmark_text_lengths(self) -> Dict[str, Any]:
        """Benchmark latency impact of different text lengths."""
        results = {}
        
        test_texts = {
            "short": "Hello",
            "medium": "This is a medium length sentence for testing TTS performance.",
            "long": "This is a much longer sentence that contains significantly more text content to test how the TTS engine performs with longer input strings and whether there are any performance degradations.",
            "very_long": "This is an extremely long piece of text that is designed to test the absolute limits of the TTS engine's performance characteristics when dealing with very large amounts of input text that might be encountered in real-world applications where users provide lengthy descriptions or explanations that need to be converted to speech with minimal latency impact on the overall user experience."
        }
        
        config = TTSPerformanceConfig()
        config.quality_mode = TTSQuality.SPEED
        
        engine = UltraFastTTSEngine(config)
        await engine.initialize()
        
        for length_category, text in test_texts.items():
            logger.info(f"  Testing {length_category} text ({len(text)} chars)")
            
            first_frame_latencies = []
            total_latencies = []
            
            for i in range(3):  # 3 tests per length
                start_time = time.time()
                first_chunk_time = None
                
                async for chunk in engine.synthesize_streaming(text):
                    if first_chunk_time is None:
                        first_chunk_time = time.time()
                
                total_latency = (time.time() - start_time) * 1000
                first_frame_latency = (first_chunk_time - start_time) * 1000 if first_chunk_time else 0
                
                first_frame_latencies.append(first_frame_latency)
                total_latencies.append(total_latency)
            
            results[length_category] = {
                "text_length": len(text),
                "avg_first_frame_ms": statistics.mean(first_frame_latencies),
                "avg_total_latency_ms": statistics.mean(total_latencies),
                "min_first_frame_ms": min(first_frame_latencies),
                "max_first_frame_ms": max(first_frame_latencies)
            }
            
            logger.info(f"    Avg first frame: {results[length_category]['avg_first_frame_ms']:.1f}ms")
        
        await engine.shutdown()
        return results
    
    async def _benchmark_caching(self) -> Dict[str, Any]:
        """Benchmark caching performance impact."""
        results = {}
        test_text = "This is a test of caching performance"
        
        # Test with caching enabled
        config_cached = TTSPerformanceConfig()
        config_cached.enable_caching = True
        config_cached.enable_pregeneration = True
        
        engine_cached = UltraFastTTSEngine(config_cached)
        await engine_cached.initialize()
        
        # First run (cache miss)
        start_time = time.time()
        first_chunk_time = None
        async for chunk in engine_cached.synthesize_streaming(test_text):
            if first_chunk_time is None:
                first_chunk_time = time.time()
        
        cache_miss_latency = (first_chunk_time - start_time) * 1000 if first_chunk_time else 0
        
        # Second run (cache hit)
        start_time = time.time()
        first_chunk_time = None
        async for chunk in engine_cached.synthesize_streaming(test_text):
            if first_chunk_time is None:
                first_chunk_time = time.time()
        
        cache_hit_latency = (first_chunk_time - start_time) * 1000 if first_chunk_time else 0
        
        await engine_cached.shutdown()
        
        # Test without caching
        config_no_cache = TTSPerformanceConfig()
        config_no_cache.enable_caching = False
        
        engine_no_cache = UltraFastTTSEngine(config_no_cache)
        await engine_no_cache.initialize()
        
        no_cache_latencies = []
        for i in range(3):
            start_time = time.time()
            first_chunk_time = None
            async for chunk in engine_no_cache.synthesize_streaming(test_text):
                if first_chunk_time is None:
                    first_chunk_time = time.time()
            
            latency = (first_chunk_time - start_time) * 1000 if first_chunk_time else 0
            no_cache_latencies.append(latency)
        
        await engine_no_cache.shutdown()
        
        avg_no_cache_latency = statistics.mean(no_cache_latencies)
        
        results = {
            "cache_miss_latency_ms": cache_miss_latency,
            "cache_hit_latency_ms": cache_hit_latency,
            "no_cache_avg_latency_ms": avg_no_cache_latency,
            "cache_speedup": avg_no_cache_latency / cache_hit_latency if cache_hit_latency > 0 else 0,
            "cache_efficiency": (avg_no_cache_latency - cache_hit_latency) / avg_no_cache_latency if avg_no_cache_latency > 0 else 0
        }
        
        logger.info(f"  Cache hit: {cache_hit_latency:.1f}ms, Cache miss: {cache_miss_latency:.1f}ms")
        logger.info(f"  Cache speedup: {results['cache_speedup']:.1f}x")
        
        return results
    
    async def _benchmark_streaming(self) -> Dict[str, Any]:
        """Compare streaming vs non-streaming performance."""
        # This would require implementing non-streaming comparison
        # For now, return placeholder results
        return {
            "streaming_enabled": True,
            "note": "Streaming comparison requires non-streaming implementation"
        }
    
    async def _benchmark_quality_modes(self) -> Dict[str, Any]:
        """Benchmark different quality modes."""
        results = {}
        test_text = "This is a quality mode performance test."
        
        quality_modes = [TTSQuality.SPEED, TTSQuality.BALANCED, TTSQuality.QUALITY]
        
        for quality_mode in quality_modes:
            logger.info(f"  Testing quality mode: {quality_mode.value}")
            
            try:
                config = TTSPerformanceConfig()
                config.quality_mode = quality_mode
                
                engine = UltraFastTTSEngine(config)
                await engine.initialize()
                
                latencies = []
                for i in range(3):
                    start_time = time.time()
                    first_chunk_time = None
                    
                    async for chunk in engine.synthesize_streaming(test_text):
                        if first_chunk_time is None:
                            first_chunk_time = time.time()
                    
                    latency = (first_chunk_time - start_time) * 1000 if first_chunk_time else 0
                    latencies.append(latency)
                
                await engine.shutdown()
                
                results[quality_mode.value] = {
                    "avg_first_frame_ms": statistics.mean(latencies),
                    "min_first_frame_ms": min(latencies),
                    "max_first_frame_ms": max(latencies)
                }
                
                logger.info(f"    Avg first frame: {results[quality_mode.value]['avg_first_frame_ms']:.1f}ms")
                
            except Exception as e:
                logger.error(f"    Quality mode {quality_mode.value} failed: {e}")
                results[quality_mode.value] = {"error": str(e)}
        
        return results
    
    async def _benchmark_concurrent(self) -> Dict[str, Any]:
        """Benchmark concurrent synthesis performance."""
        results = {}
        test_text = "Concurrent synthesis test"
        
        concurrency_levels = [1, 2, 4]
        
        for concurrency in concurrency_levels:
            logger.info(f"  Testing {concurrency} concurrent requests")
            
            try:
                config = TTSPerformanceConfig()
                engines = []
                
                # Create multiple engines
                for i in range(concurrency):
                    engine = UltraFastTTSEngine(config)
                    await engine.initialize()
                    engines.append(engine)
                
                # Run concurrent synthesis
                start_time = time.time()
                
                async def synthesize_task(engine):
                    first_chunk_time = None
                    async for chunk in engine.synthesize_streaming(test_text):
                        if first_chunk_time is None:
                            first_chunk_time = time.time()
                            return first_chunk_time
                    return None
                
                tasks = [synthesize_task(engine) for engine in engines]
                first_chunk_times = await asyncio.gather(*tasks)
                
                # Calculate metrics
                valid_times = [t for t in first_chunk_times if t is not None]
                if valid_times:
                    first_frame_latencies = [(t - start_time) * 1000 for t in valid_times]
                    
                    results[f"concurrent_{concurrency}"] = {
                        "concurrency": concurrency,
                        "avg_first_frame_ms": statistics.mean(first_frame_latencies),
                        "max_first_frame_ms": max(first_frame_latencies),
                        "successful_requests": len(valid_times)
                    }
                    
                    logger.info(f"    Avg first frame: {results[f'concurrent_{concurrency}']['avg_first_frame_ms']:.1f}ms")
                
                # Cleanup
                for engine in engines:
                    await engine.shutdown()
                
            except Exception as e:
                logger.error(f"    Concurrency {concurrency} test failed: {e}")
                results[f"concurrent_{concurrency}"] = {"error": str(e)}
        
        return results
    
    async def _benchmark_realworld(self) -> Dict[str, Any]:
        """Benchmark real-world usage scenarios."""
        results = {}
        
        # Simulate conversation responses
        conversation_responses = [
            "I understand what you're saying.",
            "That's a great point.",
            "Can you tell me more about that?",
            "I see where you're coming from.",
            "That makes perfect sense."
        ]
        
        config = TTSPerformanceConfig()
        config.enable_caching = True
        config.enable_pregeneration = True
        
        engine = UltraFastTTSEngine(config)
        await engine.initialize()
        
        # Test rapid-fire responses (simulating conversation)
        rapid_fire_latencies = []
        
        for response in conversation_responses:
            start_time = time.time()
            first_chunk_time = None
            
            async for chunk in engine.synthesize_streaming(response):
                if first_chunk_time is None:
                    first_chunk_time = time.time()
                    break
            
            if first_chunk_time:
                latency = (first_chunk_time - start_time) * 1000
                rapid_fire_latencies.append(latency)
            
            # Small delay between responses
            await asyncio.sleep(0.1)
        
        await engine.shutdown()
        
        results = {
            "conversation_simulation": {
                "avg_response_latency_ms": statistics.mean(rapid_fire_latencies) if rapid_fire_latencies else 0,
                "min_response_latency_ms": min(rapid_fire_latencies) if rapid_fire_latencies else 0,
                "max_response_latency_ms": max(rapid_fire_latencies) if rapid_fire_latencies else 0,
                "responses_tested": len(rapid_fire_latencies)
            }
        }
        
        logger.info(f"  Conversation avg latency: {results['conversation_simulation']['avg_response_latency_ms']:.1f}ms")
        
        return results
    
    def save_results(self, filename: str = "tts_latency_benchmark_results.json") -> None:
        """Save benchmark results to file."""
        try:
            with open(filename, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            logger.info(f"✅ Benchmark results saved to {filename}")
        except Exception as e:
            logger.error(f"❌ Failed to save results: {e}")
    
    def print_summary(self) -> None:
        """Print benchmark summary."""
        print("\n" + "="*60)
        print("🔊 TTS LATENCY BENCHMARK SUMMARY")
        print("="*60)
        
        # First frame latency
        if "first_frame_latency" in self.results:
            print("\n⚡ First Frame Latency:")
            for config, data in self.results["first_frame_latency"].items():
                if "error" not in data:
                    target_met = "✅" if data.get("target_met", False) else "⚠️"
                    print(f"  {target_met} {config}: {data['avg_first_frame_ms']:.1f}ms")
        
        # Provider comparison
        if "provider_comparison" in self.results:
            print("\n📊 Provider Performance:")
            for provider, data in self.results["provider_comparison"].items():
                if "error" not in data:
                    print(f"  {provider}: {data['avg_first_frame_ms']:.1f}ms first frame")
        
        # Caching performance
        if "caching_performance" in self.results:
            cache_data = self.results["caching_performance"]
            if "error" not in cache_data:
                print(f"\n💾 Caching Performance:")
                print(f"  Cache hit: {cache_data['cache_hit_latency_ms']:.1f}ms")
                print(f"  Cache miss: {cache_data['cache_miss_latency_ms']:.1f}ms")
                print(f"  Speedup: {cache_data['cache_speedup']:.1f}x")
        
        # Real-world scenarios
        if "realworld_scenarios" in self.results:
            rw_data = self.results["realworld_scenarios"]
            if "conversation_simulation" in rw_data:
                conv_data = rw_data["conversation_simulation"]
                print(f"\n🌍 Conversation Simulation:")
                print(f"  Avg response: {conv_data['avg_response_latency_ms']:.1f}ms")
        
        print("="*60)


async def main():
    """Run TTS latency benchmark."""
    benchmark = TTSLatencyBenchmark()
    
    try:
        results = await benchmark.run_comprehensive_benchmark()
        
        # Print summary
        benchmark.print_summary()
        
        # Save results
        benchmark.save_results()
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Benchmark failed: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
