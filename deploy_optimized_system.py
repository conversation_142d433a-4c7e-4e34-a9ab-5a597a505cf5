#!/usr/bin/env python3
"""
DEPLOY OPTIMIZED VOICE AI SYSTEM
Complete deployment script with testing and validation
"""

import asyncio
import logging
import time
import json
import sys
from pathlib import Path
from typing import Dict, Any, List

# Import all our optimized components
from unified_config_system import migrate_legacy_configs, get_config, save_config
from ultra_performance_optimizer import UltraPerformanceOptimizer
from unified_voice_ai_system import UnifiedVoiceAI
from comprehensive_error_handler import get_error_handler

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("deployment")

class SystemDeployment:
    """Complete system deployment and validation."""
    
    def __init__(self):
        self.deployment_start = time.time()
        self.validation_results = {}
        self.performance_results = {}
        
    async def deploy_complete_system(self) -> bool:
        """Deploy the complete optimized voice AI system."""
        logger.info("🚀 DEPLOYING OPTIMIZED VOICE AI SYSTEM")
        logger.info("=" * 70)
        
        try:
            # Phase 1: Configuration Migration and Setup
            logger.info("📋 Phase 1: Configuration Setup")
            if not await self._setup_configuration():
                return False
            
            # Phase 2: System Optimization
            logger.info("⚡ Phase 2: System Optimization")
            if not await self._run_system_optimization():
                return False
            
            # Phase 3: Component Validation
            logger.info("🔍 Phase 3: Component Validation")
            if not await self._validate_components():
                return False
            
            # Phase 4: Performance Testing
            logger.info("🎯 Phase 4: Performance Testing")
            if not await self._run_performance_tests():
                return False
            
            # Phase 5: Integration Testing
            logger.info("🔄 Phase 5: Integration Testing")
            if not await self._run_integration_tests():
                return False
            
            # Phase 6: Final Deployment
            logger.info("✅ Phase 6: Final Deployment")
            if not await self._finalize_deployment():
                return False
            
            # Generate deployment report
            await self._generate_deployment_report()
            
            deployment_time = time.time() - self.deployment_start
            logger.info("=" * 70)
            logger.info("🎉 DEPLOYMENT SUCCESSFUL!")
            logger.info(f"⏱️  Total deployment time: {deployment_time:.1f}s")
            logger.info("=" * 70)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Deployment failed: {e}")
            return False
    
    async def _setup_configuration(self) -> bool:
        """Setup and migrate configuration."""
        try:
            logger.info("  📁 Migrating legacy configurations...")
            migrate_legacy_configs()
            
            logger.info("  ⚙️ Loading unified configuration...")
            config = get_config()
            
            logger.info("  💾 Saving optimized configuration...")
            save_config(config)
            
            logger.info("  ✅ Configuration setup complete")
            return True
            
        except Exception as e:
            logger.error(f"  ❌ Configuration setup failed: {e}")
            return False
    
    async def _run_system_optimization(self) -> bool:
        """Run comprehensive system optimization."""
        try:
            logger.info("  🔧 Running ultra performance optimization...")
            
            optimizer = UltraPerformanceOptimizer()
            results = await optimizer.run_comprehensive_optimization()
            
            self.performance_results["optimization"] = results
            
            logger.info(f"  ✅ System optimization complete ({results['optimization_time']:.1f}s)")
            return True
            
        except Exception as e:
            logger.error(f"  ❌ System optimization failed: {e}")
            return False
    
    async def _validate_components(self) -> bool:
        """Validate all system components."""
        try:
            validation_results = {}
            
            # Validate audio backends
            logger.info("  🎵 Validating audio backends...")
            validation_results["audio"] = await self._validate_audio_backends()
            
            # Validate STT engine
            logger.info("  🎤 Validating STT engine...")
            validation_results["stt"] = await self._validate_stt_engine()
            
            # Validate LLM engine
            logger.info("  🧠 Validating LLM engine...")
            validation_results["llm"] = await self._validate_llm_engine()
            
            # Validate TTS engine
            logger.info("  🔊 Validating TTS engine...")
            validation_results["tts"] = await self._validate_tts_engine()
            
            self.validation_results = validation_results
            
            # Check if all validations passed
            all_passed = all(result.get("status") == "success" for result in validation_results.values())
            
            if all_passed:
                logger.info("  ✅ All component validations passed")
                return True
            else:
                logger.warning("  ⚠️ Some component validations failed")
                return False
            
        except Exception as e:
            logger.error(f"  ❌ Component validation failed: {e}")
            return False
    
    async def _validate_audio_backends(self) -> Dict[str, Any]:
        """Validate audio backends."""
        results = {"status": "success", "backends": []}
        
        # Test pygame
        try:
            import pygame
            pygame.mixer.init(frequency=22050, size=-16, channels=1, buffer=512)
            pygame.mixer.quit()
            results["backends"].append("pygame")
        except Exception as e:
            logger.warning(f"    pygame validation failed: {e}")
        
        # Test simpleaudio
        try:
            import simpleaudio
            results["backends"].append("simpleaudio")
        except Exception as e:
            logger.warning(f"    simpleaudio validation failed: {e}")
        
        # Test sounddevice
        try:
            import sounddevice as sd
            devices = sd.query_devices()
            results["backends"].append("sounddevice")
            results["audio_devices"] = len(devices)
        except Exception as e:
            logger.warning(f"    sounddevice validation failed: {e}")
        
        if not results["backends"]:
            results["status"] = "failed"
            results["error"] = "No audio backends available"
        
        return results
    
    async def _validate_stt_engine(self) -> Dict[str, Any]:
        """Validate STT engine."""
        try:
            from faster_whisper import WhisperModel
            
            # Test model loading
            model = WhisperModel("tiny.en", device="cpu", compute_type="int8")
            
            # Test transcription with dummy audio
            import numpy as np
            test_audio = np.zeros(16000, dtype=np.float32)  # 1 second of silence
            segments, _ = model.transcribe(test_audio, language="en")
            list(segments)  # Consume generator
            
            return {"status": "success", "model": "tiny.en", "device": "cpu"}
            
        except Exception as e:
            return {"status": "failed", "error": str(e)}
    
    async def _validate_llm_engine(self) -> Dict[str, Any]:
        """Validate LLM engine."""
        try:
            import ollama
            
            # Test connection
            models = ollama.list()
            if not models.models:
                return {"status": "failed", "error": "No Ollama models available"}
            
            # Test simple query
            response = ollama.chat(
                model=models.models[0].model,
                messages=[{"role": "user", "content": "Hello"}]
            )
            
            return {
                "status": "success",
                "model": models.models[0].model,
                "models_available": len(models.models)
            }
            
        except Exception as e:
            return {"status": "failed", "error": str(e)}
    
    async def _validate_tts_engine(self) -> Dict[str, Any]:
        """Validate TTS engine."""
        try:
            import edge_tts
            
            # Test TTS generation
            communicate = edge_tts.Communicate("Hello", "en-US-AriaNeural")
            
            audio_generated = False
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_generated = True
                    break
            
            if not audio_generated:
                return {"status": "failed", "error": "No audio generated"}
            
            return {"status": "success", "voice": "en-US-AriaNeural"}
            
        except Exception as e:
            return {"status": "failed", "error": str(e)}
    
    async def _run_performance_tests(self) -> bool:
        """Run performance tests."""
        try:
            logger.info("  📊 Running performance baseline tests...")
            
            # Import and run performance tests
            from comprehensive_performance_test import PerformanceBaseline
            
            baseline = PerformanceBaseline()
            results = await baseline.run_comprehensive_baseline()
            
            self.performance_results["baseline"] = results
            
            # Check if performance meets targets
            summary = results.get("summary", {})
            key_metrics = summary.get("key_metrics", {})
            
            performance_ok = True
            
            # Check STT performance
            if "stt_rtf" in key_metrics and key_metrics["stt_rtf"] > 0.5:
                logger.warning(f"    STT RTF high: {key_metrics['stt_rtf']:.3f}")
                performance_ok = False
            
            # Check TTS performance
            if "tts_first_frame_ms" in key_metrics and key_metrics["tts_first_frame_ms"] > 200:
                logger.warning(f"    TTS latency high: {key_metrics['tts_first_frame_ms']:.1f}ms")
                performance_ok = False
            
            if performance_ok:
                logger.info("  ✅ Performance tests passed")
            else:
                logger.warning("  ⚠️ Performance tests show issues")
            
            return True  # Continue deployment even with performance warnings
            
        except Exception as e:
            logger.error(f"  ❌ Performance tests failed: {e}")
            return False
    
    async def _run_integration_tests(self) -> bool:
        """Run integration tests."""
        try:
            logger.info("  🔄 Running integration tests...")
            
            # Test unified voice AI system
            system = UnifiedVoiceAI()
            
            # Initialize system
            init_success = await system.initialize()
            if not init_success:
                logger.error("    System initialization failed")
                return False
            
            # Get system status
            status = system.get_system_status()
            
            # Test error handling
            error_handler = get_error_handler()
            test_error = ValueError("Test error")
            await error_handler.handle_error(test_error, "test_component")
            
            # Shutdown system
            await system.shutdown()
            
            self.validation_results["integration"] = {
                "status": "success",
                "system_state": status["state"],
                "components_initialized": True
            }
            
            logger.info("  ✅ Integration tests passed")
            return True
            
        except Exception as e:
            logger.error(f"  ❌ Integration tests failed: {e}")
            self.validation_results["integration"] = {
                "status": "failed",
                "error": str(e)
            }
            return False
    
    async def _finalize_deployment(self) -> bool:
        """Finalize deployment."""
        try:
            logger.info("  📦 Finalizing deployment...")
            
            # Create deployment marker
            deployment_info = {
                "deployment_time": time.time(),
                "version": "1.0.0-optimized",
                "components": ["audio_pipeline", "stt_engine", "llm_engine", "tts_engine"],
                "optimizations_applied": [
                    "memory_management",
                    "task_leak_prevention",
                    "audio_buffer_pooling",
                    "configuration_consolidation",
                    "error_handling_enhancement"
                ]
            }
            
            with open("deployment_info.json", "w") as f:
                json.dump(deployment_info, f, indent=2)
            
            logger.info("  ✅ Deployment finalized")
            return True
            
        except Exception as e:
            logger.error(f"  ❌ Deployment finalization failed: {e}")
            return False
    
    async def _generate_deployment_report(self) -> None:
        """Generate comprehensive deployment report."""
        report = {
            "deployment_summary": {
                "timestamp": time.time(),
                "duration": time.time() - self.deployment_start,
                "status": "success"
            },
            "validation_results": self.validation_results,
            "performance_results": self.performance_results,
            "system_info": {
                "python_version": sys.version,
                "platform": sys.platform
            }
        }
        
        try:
            with open("deployment_report.json", "w") as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info("📋 Deployment report saved to deployment_report.json")
            
            # Print summary
            print("\n" + "="*70)
            print("📋 DEPLOYMENT SUMMARY")
            print("="*70)
            print(f"Duration: {report['deployment_summary']['duration']:.1f}s")
            print(f"Status: {report['deployment_summary']['status']}")
            
            # Component validation summary
            print("\n🔍 Component Validation:")
            for component, result in self.validation_results.items():
                status = result.get("status", "unknown")
                print(f"  {component}: {status}")
            
            # Performance summary
            if "baseline" in self.performance_results:
                baseline = self.performance_results["baseline"]
                summary = baseline.get("summary", {})
                metrics = summary.get("key_metrics", {})
                
                print("\n📊 Performance Metrics:")
                if "stt_rtf" in metrics:
                    print(f"  STT RTF: {metrics['stt_rtf']:.3f}")
                if "tts_first_frame_ms" in metrics:
                    print(f"  TTS First Frame: {metrics['tts_first_frame_ms']:.1f}ms")
                if "e2e_total_time" in metrics:
                    print(f"  End-to-End: {metrics['e2e_total_time']:.3f}s")
            
            print("="*70)
            
        except Exception as e:
            logger.error(f"Failed to generate deployment report: {e}")


async def main():
    """Run complete system deployment."""
    deployment = SystemDeployment()
    
    success = await deployment.deploy_complete_system()
    
    if success:
        print("\n🎉 OPTIMIZED VOICE AI SYSTEM DEPLOYED SUCCESSFULLY!")
        print("🚀 System is ready for production use.")
        return 0
    else:
        print("\n❌ DEPLOYMENT FAILED!")
        print("🔧 Please check the logs and fix any issues.")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
