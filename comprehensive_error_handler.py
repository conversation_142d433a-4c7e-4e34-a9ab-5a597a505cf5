#!/usr/bin/env python3
"""
COMPREHENSIVE ERROR HANDLING AND RECOVERY SYSTEM
Robust error handling with automatic recovery mechanisms
"""

import asyncio
import logging
import time
import traceback
import functools
from typing import Dict, Any, Optional, List, Callable, Union, Type
from dataclasses import dataclass, field
from enum import Enum
import threading
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""
    RETRY = "retry"
    FALLBACK = "fallback"
    RESTART = "restart"
    IGNORE = "ignore"
    ESCALATE = "escalate"

@dataclass
class ErrorInfo:
    """Comprehensive error information."""
    error_type: str
    error_message: str
    severity: ErrorSeverity
    timestamp: float
    component: str
    context: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    recovery_attempted: bool = False
    recovery_successful: bool = False
    retry_count: int = 0

class ErrorRecoveryManager:
    """Comprehensive error recovery management."""
    
    def __init__(self):
        self.error_history: deque = deque(maxlen=1000)
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.recovery_strategies: Dict[str, RecoveryStrategy] = {}
        self.fallback_handlers: Dict[str, Callable] = {}
        self.circuit_breakers: Dict[str, 'CircuitBreaker'] = {}
        self._lock = threading.Lock()
        
        # Default recovery strategies
        self._setup_default_strategies()
    
    def _setup_default_strategies(self) -> None:
        """Setup default recovery strategies."""
        self.recovery_strategies.update({
            "ConnectionError": RecoveryStrategy.RETRY,
            "TimeoutError": RecoveryStrategy.RETRY,
            "MemoryError": RecoveryStrategy.RESTART,
            "ImportError": RecoveryStrategy.FALLBACK,
            "FileNotFoundError": RecoveryStrategy.FALLBACK,
            "PermissionError": RecoveryStrategy.ESCALATE,
            "KeyboardInterrupt": RecoveryStrategy.IGNORE,
            "SystemExit": RecoveryStrategy.IGNORE,
        })
    
    def register_error(self, error: Exception, component: str, context: Dict[str, Any] = None) -> ErrorInfo:
        """Register an error and determine recovery strategy."""
        error_info = ErrorInfo(
            error_type=type(error).__name__,
            error_message=str(error),
            severity=self._determine_severity(error),
            timestamp=time.time(),
            component=component,
            context=context or {},
            stack_trace=traceback.format_exc()
        )
        
        with self._lock:
            self.error_history.append(error_info)
            self.error_counts[error_info.error_type] += 1
        
        logger.error(f"Error registered: {error_info.error_type} in {component}: {error_info.error_message}")
        
        return error_info
    
    def _determine_severity(self, error: Exception) -> ErrorSeverity:
        """Determine error severity based on error type."""
        critical_errors = (MemoryError, SystemExit, KeyboardInterrupt)
        high_errors = (ConnectionError, OSError, ImportError)
        medium_errors = (TimeoutError, ValueError, TypeError)
        
        if isinstance(error, critical_errors):
            return ErrorSeverity.CRITICAL
        elif isinstance(error, high_errors):
            return ErrorSeverity.HIGH
        elif isinstance(error, medium_errors):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def get_recovery_strategy(self, error_info: ErrorInfo) -> RecoveryStrategy:
        """Get recovery strategy for an error."""
        # Check if we have a specific strategy for this error type
        strategy = self.recovery_strategies.get(error_info.error_type)
        
        if strategy is None:
            # Default strategy based on severity
            if error_info.severity == ErrorSeverity.CRITICAL:
                strategy = RecoveryStrategy.RESTART
            elif error_info.severity == ErrorSeverity.HIGH:
                strategy = RecoveryStrategy.RETRY
            else:
                strategy = RecoveryStrategy.FALLBACK
        
        # Check error frequency - if too many errors, escalate
        if self.error_counts[error_info.error_type] > 10:
            strategy = RecoveryStrategy.ESCALATE
        
        return strategy
    
    def register_fallback_handler(self, error_type: str, handler: Callable) -> None:
        """Register a fallback handler for specific error types."""
        self.fallback_handlers[error_type] = handler
        logger.info(f"Registered fallback handler for {error_type}")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        with self._lock:
            recent_errors = [e for e in self.error_history if time.time() - e.timestamp < 3600]  # Last hour
            
            return {
                "total_errors": len(self.error_history),
                "recent_errors": len(recent_errors),
                "error_counts": dict(self.error_counts),
                "most_common_errors": sorted(self.error_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            }

class CircuitBreaker:
    """Circuit breaker pattern for error handling."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0, expected_exception: Type[Exception] = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self._lock = threading.Lock()
    
    def __call__(self, func: Callable) -> Callable:
        """Decorator to apply circuit breaker to a function."""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return self.call(func, *args, **kwargs)
        return wrapper
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Call function with circuit breaker protection."""
        with self._lock:
            if self.state == "OPEN":
                if self._should_attempt_reset():
                    self.state = "HALF_OPEN"
                else:
                    raise Exception(f"Circuit breaker is OPEN for {func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            
            except self.expected_exception as e:
                self._on_failure()
                raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset the circuit breaker."""
        return (
            self.last_failure_time is not None and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self) -> None:
        """Handle successful function call."""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self) -> None:
        """Handle failed function call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"

def resilient_async(max_retries: int = 3, backoff_factor: float = 1.0, exceptions: tuple = (Exception,)):
    """Decorator for resilient async functions with exponential backoff."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries: {e}")
                        raise e
                    
                    # Calculate backoff delay
                    delay = backoff_factor * (2 ** attempt)
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}), retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
            
            raise last_exception
        
        return wrapper
    return decorator

def resilient_sync(max_retries: int = 3, backoff_factor: float = 1.0, exceptions: tuple = (Exception,)):
    """Decorator for resilient sync functions with exponential backoff."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries: {e}")
                        raise e
                    
                    # Calculate backoff delay
                    delay = backoff_factor * (2 ** attempt)
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}), retrying in {delay}s: {e}")
                    time.sleep(delay)
            
            raise last_exception
        
        return wrapper
    return decorator

class VoiceAIErrorHandler:
    """Specialized error handler for voice AI components."""
    
    def __init__(self):
        self.recovery_manager = ErrorRecoveryManager()
        self.component_handlers: Dict[str, Callable] = {}
        
        # Setup component-specific handlers
        self._setup_component_handlers()
    
    def _setup_component_handlers(self) -> None:
        """Setup component-specific error handlers."""
        self.component_handlers.update({
            "stt": self._handle_stt_error,
            "tts": self._handle_tts_error,
            "llm": self._handle_llm_error,
            "audio": self._handle_audio_error,
            "memory": self._handle_memory_error,
        })
    
    async def handle_error(self, error: Exception, component: str, context: Dict[str, Any] = None) -> bool:
        """Handle an error with appropriate recovery strategy."""
        error_info = self.recovery_manager.register_error(error, component, context)
        strategy = self.recovery_manager.get_recovery_strategy(error_info)
        
        logger.info(f"Handling {error_info.error_type} in {component} with strategy: {strategy.value}")
        
        try:
            if strategy == RecoveryStrategy.RETRY:
                return await self._retry_operation(error_info, context)
            elif strategy == RecoveryStrategy.FALLBACK:
                return await self._fallback_operation(error_info, context)
            elif strategy == RecoveryStrategy.RESTART:
                return await self._restart_component(error_info, context)
            elif strategy == RecoveryStrategy.IGNORE:
                logger.info(f"Ignoring error: {error_info.error_message}")
                return True
            elif strategy == RecoveryStrategy.ESCALATE:
                return await self._escalate_error(error_info, context)
            
        except Exception as recovery_error:
            logger.error(f"Recovery failed for {error_info.error_type}: {recovery_error}")
            error_info.recovery_attempted = True
            error_info.recovery_successful = False
            return False
        
        return False
    
    async def _retry_operation(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """Retry the failed operation."""
        if error_info.retry_count >= 3:
            logger.warning(f"Max retries exceeded for {error_info.error_type}")
            return False
        
        error_info.retry_count += 1
        error_info.recovery_attempted = True
        
        # Wait before retry with exponential backoff
        delay = 2 ** error_info.retry_count
        await asyncio.sleep(delay)
        
        # Component-specific retry logic
        handler = self.component_handlers.get(error_info.component)
        if handler:
            return await handler(error_info, context, "retry")
        
        return False
    
    async def _fallback_operation(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """Execute fallback operation."""
        error_info.recovery_attempted = True
        
        # Check for registered fallback handler
        fallback_handler = self.recovery_manager.fallback_handlers.get(error_info.error_type)
        if fallback_handler:
            try:
                result = await fallback_handler(error_info, context)
                error_info.recovery_successful = True
                return result
            except Exception as e:
                logger.error(f"Fallback handler failed: {e}")
        
        # Component-specific fallback logic
        handler = self.component_handlers.get(error_info.component)
        if handler:
            return await handler(error_info, context, "fallback")
        
        return False
    
    async def _restart_component(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """Restart the failed component."""
        error_info.recovery_attempted = True
        
        logger.warning(f"Restarting component: {error_info.component}")
        
        # Component-specific restart logic
        handler = self.component_handlers.get(error_info.component)
        if handler:
            return await handler(error_info, context, "restart")
        
        return False
    
    async def _escalate_error(self, error_info: ErrorInfo, context: Dict[str, Any]) -> bool:
        """Escalate error to higher level handling."""
        error_info.recovery_attempted = True
        
        logger.critical(f"Escalating error: {error_info.error_type} in {error_info.component}")
        
        # In a real system, this might send alerts, create tickets, etc.
        # For now, we'll just log and return False
        return False
    
    async def _handle_stt_error(self, error_info: ErrorInfo, context: Dict[str, Any], action: str) -> bool:
        """Handle STT-specific errors."""
        if action == "fallback":
            # Try different STT model or provider
            logger.info("Falling back to alternative STT model")
            return True
        elif action == "restart":
            # Restart STT service
            logger.info("Restarting STT service")
            return True
        return False
    
    async def _handle_tts_error(self, error_info: ErrorInfo, context: Dict[str, Any], action: str) -> bool:
        """Handle TTS-specific errors."""
        if action == "fallback":
            # Try different TTS voice or provider
            logger.info("Falling back to alternative TTS voice")
            return True
        elif action == "restart":
            # Restart TTS service
            logger.info("Restarting TTS service")
            return True
        return False
    
    async def _handle_llm_error(self, error_info: ErrorInfo, context: Dict[str, Any], action: str) -> bool:
        """Handle LLM-specific errors."""
        if action == "fallback":
            # Try different LLM model
            logger.info("Falling back to alternative LLM model")
            return True
        elif action == "restart":
            # Restart LLM connection
            logger.info("Restarting LLM connection")
            return True
        return False
    
    async def _handle_audio_error(self, error_info: ErrorInfo, context: Dict[str, Any], action: str) -> bool:
        """Handle audio-specific errors."""
        if action == "fallback":
            # Try different audio backend
            logger.info("Falling back to alternative audio backend")
            return True
        elif action == "restart":
            # Restart audio system
            logger.info("Restarting audio system")
            return True
        return False
    
    async def _handle_memory_error(self, error_info: ErrorInfo, context: Dict[str, Any], action: str) -> bool:
        """Handle memory-specific errors."""
        if action == "fallback":
            # Trigger aggressive cleanup
            logger.info("Triggering aggressive memory cleanup")
            import gc
            gc.collect()
            return True
        elif action == "restart":
            # This would typically restart the entire process
            logger.critical("Memory error requires process restart")
            return False
        return False
    
    def get_error_report(self) -> Dict[str, Any]:
        """Get comprehensive error report."""
        stats = self.recovery_manager.get_error_stats()
        
        # Add recovery success rates
        recovery_stats = {
            "total_recovery_attempts": 0,
            "successful_recoveries": 0,
            "recovery_success_rate": 0.0
        }
        
        for error_info in self.recovery_manager.error_history:
            if error_info.recovery_attempted:
                recovery_stats["total_recovery_attempts"] += 1
                if error_info.recovery_successful:
                    recovery_stats["successful_recoveries"] += 1
        
        if recovery_stats["total_recovery_attempts"] > 0:
            recovery_stats["recovery_success_rate"] = (
                recovery_stats["successful_recoveries"] / recovery_stats["total_recovery_attempts"]
            )
        
        return {
            "error_stats": stats,
            "recovery_stats": recovery_stats,
            "timestamp": time.time()
        }


# Global error handler instance
_error_handler: Optional[VoiceAIErrorHandler] = None

def get_error_handler() -> VoiceAIErrorHandler:
    """Get the global error handler instance."""
    global _error_handler
    if _error_handler is None:
        _error_handler = VoiceAIErrorHandler()
    return _error_handler

async def handle_voice_ai_error(error: Exception, component: str, context: Dict[str, Any] = None) -> bool:
    """Convenience function to handle voice AI errors."""
    handler = get_error_handler()
    return await handler.handle_error(error, component, context)


# Example usage decorators
@resilient_async(max_retries=3, backoff_factor=1.0, exceptions=(ConnectionError, TimeoutError))
async def example_stt_function():
    """Example STT function with resilient decorator."""
    # Simulate STT processing
    await asyncio.sleep(0.1)
    return "transcribed text"

@CircuitBreaker(failure_threshold=5, recovery_timeout=60.0, expected_exception=ConnectionError)
def example_llm_function():
    """Example LLM function with circuit breaker."""
    # Simulate LLM processing
    return "generated response"


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    async def test_error_handling():
        """Test the error handling system."""
        handler = get_error_handler()
        
        # Simulate various errors
        test_errors = [
            (ConnectionError("Connection failed"), "stt"),
            (MemoryError("Out of memory"), "memory"),
            (TimeoutError("Request timeout"), "llm"),
            (ValueError("Invalid input"), "audio"),
        ]
        
        for error, component in test_errors:
            success = await handler.handle_error(error, component)
            print(f"Error handling for {component}: {'Success' if success else 'Failed'}")
        
        # Print error report
        report = handler.get_error_report()
        print("\n🛡️ ERROR HANDLING REPORT")
        print(f"Total errors: {report['error_stats']['total_errors']}")
        print(f"Recovery attempts: {report['recovery_stats']['total_recovery_attempts']}")
        print(f"Recovery success rate: {report['recovery_stats']['recovery_success_rate']:.2%}")
    
    asyncio.run(test_error_handling())
