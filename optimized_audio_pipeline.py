#!/usr/bin/env python3
"""
OPTIMIZED AUDIO PIPELINE
High-performance audio processing with buffer pooling and streaming optimization
"""

import asyncio
import logging
import time
import threading
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass
from enum import Enum
import numpy as np
from collections import deque
import weakref

logger = logging.getLogger(__name__)

class AudioFormat(Enum):
    """Supported audio formats."""
    PCM_16 = "pcm_16"
    PCM_24 = "pcm_24"
    PCM_32 = "pcm_32"
    FLOAT_32 = "float_32"

@dataclass
class AudioChunk:
    """Optimized audio chunk with metadata."""
    data: np.ndarray
    sample_rate: int
    channels: int
    timestamp: float
    format: AudioFormat
    
    def __post_init__(self):
        self.size = len(self.data)
        self.duration = self.size / (self.sample_rate * self.channels)

class OptimizedAudioBuffer:
    """High-performance audio buffer with automatic memory management."""
    
    def __init__(self, max_size: int = 1024 * 1024, chunk_size: int = 4096):
        self.max_size = max_size
        self.chunk_size = chunk_size
        self._buffer = deque(maxlen=max_size // chunk_size)
        self._lock = threading.RLock()
        self._total_size = 0
        self._overflow_count = 0
        
    def write(self, chunk: AudioChunk) -> bool:
        """Write audio chunk to buffer."""
        with self._lock:
            if self._total_size + chunk.size > self.max_size:
                # Remove oldest chunks to make space
                while self._buffer and self._total_size + chunk.size > self.max_size:
                    old_chunk = self._buffer.popleft()
                    self._total_size -= old_chunk.size
                    self._overflow_count += 1
                
                if self._overflow_count % 100 == 0:
                    logger.warning(f"Audio buffer overflow: {self._overflow_count} chunks dropped")
            
            self._buffer.append(chunk)
            self._total_size += chunk.size
            return True
    
    def read(self, max_chunks: int = None) -> List[AudioChunk]:
        """Read audio chunks from buffer."""
        with self._lock:
            if max_chunks is None:
                chunks = list(self._buffer)
                self._buffer.clear()
                self._total_size = 0
            else:
                chunks = []
                for _ in range(min(max_chunks, len(self._buffer))):
                    chunk = self._buffer.popleft()
                    chunks.append(chunk)
                    self._total_size -= chunk.size
            
            return chunks
    
    def peek(self, num_chunks: int = 1) -> List[AudioChunk]:
        """Peek at chunks without removing them."""
        with self._lock:
            return list(self._buffer)[:num_chunks]
    
    def clear(self) -> None:
        """Clear the buffer."""
        with self._lock:
            self._buffer.clear()
            self._total_size = 0
    
    @property
    def size(self) -> int:
        """Get current buffer size in bytes."""
        return self._total_size
    
    @property
    def chunk_count(self) -> int:
        """Get number of chunks in buffer."""
        return len(self._buffer)

class AudioBufferPool:
    """Memory-efficient audio buffer pool."""
    
    def __init__(self, max_buffers: int = 100, buffer_size: int = 4096):
        self.max_buffers = max_buffers
        self.buffer_size = buffer_size
        self._available_buffers = deque()
        self._in_use_buffers = weakref.WeakSet()
        self._lock = threading.Lock()
        self._created_count = 0
        self._reuse_count = 0
        
        # Pre-allocate some buffers
        self._preallocate_buffers(min(10, max_buffers))
    
    def _preallocate_buffers(self, count: int) -> None:
        """Pre-allocate buffers for better performance."""
        for _ in range(count):
            buffer = np.zeros(self.buffer_size, dtype=np.float32)
            self._available_buffers.append(buffer)
            self._created_count += 1
    
    def get_buffer(self, size: int = None) -> np.ndarray:
        """Get a buffer from the pool."""
        if size is None:
            size = self.buffer_size
        
        with self._lock:
            # Try to reuse existing buffer
            if self._available_buffers and size <= self.buffer_size:
                buffer = self._available_buffers.popleft()
                self._in_use_buffers.add(buffer)
                self._reuse_count += 1
                return buffer[:size] if size < len(buffer) else buffer
            
            # Create new buffer if pool not full
            if self._created_count < self.max_buffers:
                buffer = np.zeros(max(size, self.buffer_size), dtype=np.float32)
                self._in_use_buffers.add(buffer)
                self._created_count += 1
                return buffer[:size] if size < len(buffer) else buffer
            
            # Pool is full, create temporary buffer
            logger.warning("Audio buffer pool exhausted, creating temporary buffer")
            return np.zeros(size, dtype=np.float32)
    
    def return_buffer(self, buffer: np.ndarray) -> None:
        """Return a buffer to the pool."""
        with self._lock:
            if len(self._available_buffers) < self.max_buffers:
                # Reset buffer and return to pool
                buffer.fill(0)
                self._available_buffers.append(buffer)
            
            # Remove from in-use set
            self._in_use_buffers.discard(buffer)
    
    def get_stats(self) -> Dict[str, int]:
        """Get buffer pool statistics."""
        with self._lock:
            return {
                "created_count": self._created_count,
                "reuse_count": self._reuse_count,
                "available_buffers": len(self._available_buffers),
                "in_use_buffers": len(self._in_use_buffers),
                "max_buffers": self.max_buffers
            }

class StreamingAudioProcessor:
    """High-performance streaming audio processor."""
    
    def __init__(self, sample_rate: int = 16000, channels: int = 1, chunk_size: int = 1024):
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_size = chunk_size
        
        # Audio processing components
        self.input_buffer = OptimizedAudioBuffer(max_size=1024*1024, chunk_size=chunk_size)
        self.output_buffer = OptimizedAudioBuffer(max_size=1024*1024, chunk_size=chunk_size)
        self.buffer_pool = AudioBufferPool(max_buffers=50, buffer_size=chunk_size)
        
        # Processing pipeline
        self.processors: List[Callable[[AudioChunk], AudioChunk]] = []
        self.is_processing = False
        self._processing_task = None
        self._stats = {
            "chunks_processed": 0,
            "processing_time": 0.0,
            "average_latency": 0.0
        }
    
    def add_processor(self, processor: Callable[[AudioChunk], AudioChunk]) -> None:
        """Add an audio processor to the pipeline."""
        self.processors.append(processor)
        logger.info(f"Added audio processor: {processor.__name__}")
    
    def start_processing(self) -> None:
        """Start the audio processing pipeline."""
        if self.is_processing:
            return
        
        self.is_processing = True
        self._processing_task = asyncio.create_task(self._processing_loop())
        logger.info("Audio processing pipeline started")
    
    def stop_processing(self) -> None:
        """Stop the audio processing pipeline."""
        self.is_processing = False
        if self._processing_task:
            self._processing_task.cancel()
        logger.info("Audio processing pipeline stopped")
    
    async def _processing_loop(self) -> None:
        """Main audio processing loop."""
        while self.is_processing:
            try:
                # Process available chunks
                chunks = self.input_buffer.read(max_chunks=10)
                
                if chunks:
                    for chunk in chunks:
                        processed_chunk = await self._process_chunk(chunk)
                        if processed_chunk:
                            self.output_buffer.write(processed_chunk)
                else:
                    # No chunks available, sleep briefly
                    await asyncio.sleep(0.001)
                
            except Exception as e:
                logger.error(f"Error in audio processing loop: {e}")
                await asyncio.sleep(0.01)
    
    async def _process_chunk(self, chunk: AudioChunk) -> Optional[AudioChunk]:
        """Process a single audio chunk through the pipeline."""
        start_time = time.time()
        
        try:
            processed_chunk = chunk
            
            # Apply all processors in sequence
            for processor in self.processors:
                processed_chunk = processor(processed_chunk)
                if processed_chunk is None:
                    break
            
            # Update statistics
            processing_time = time.time() - start_time
            self._stats["chunks_processed"] += 1
            self._stats["processing_time"] += processing_time
            self._stats["average_latency"] = (
                self._stats["processing_time"] / self._stats["chunks_processed"]
            )
            
            return processed_chunk
            
        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")
            return None
    
    def write_audio(self, data: np.ndarray, timestamp: float = None) -> bool:
        """Write audio data to the input buffer."""
        if timestamp is None:
            timestamp = time.time()
        
        # Create audio chunk
        chunk = AudioChunk(
            data=data,
            sample_rate=self.sample_rate,
            channels=self.channels,
            timestamp=timestamp,
            format=AudioFormat.FLOAT_32
        )
        
        return self.input_buffer.write(chunk)
    
    def read_audio(self, max_chunks: int = None) -> List[AudioChunk]:
        """Read processed audio from the output buffer."""
        return self.output_buffer.read(max_chunks)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        buffer_stats = self.buffer_pool.get_stats()
        
        return {
            "processing_stats": self._stats.copy(),
            "buffer_pool_stats": buffer_stats,
            "input_buffer_size": self.input_buffer.size,
            "output_buffer_size": self.output_buffer.size,
            "input_chunk_count": self.input_buffer.chunk_count,
            "output_chunk_count": self.output_buffer.chunk_count
        }

# Audio processing functions
def noise_reduction_processor(chunk: AudioChunk) -> AudioChunk:
    """Simple noise reduction processor."""
    # Apply basic noise gate
    threshold = 0.01
    data = chunk.data.copy()
    data[np.abs(data) < threshold] *= 0.1
    
    return AudioChunk(
        data=data,
        sample_rate=chunk.sample_rate,
        channels=chunk.channels,
        timestamp=chunk.timestamp,
        format=chunk.format
    )

def volume_normalization_processor(chunk: AudioChunk) -> AudioChunk:
    """Volume normalization processor."""
    data = chunk.data.copy()
    
    # Calculate RMS
    rms = np.sqrt(np.mean(data**2))
    
    if rms > 0:
        # Normalize to target RMS
        target_rms = 0.1
        gain = target_rms / rms
        # Limit gain to prevent clipping
        gain = min(gain, 1.0 / np.max(np.abs(data)))
        data *= gain
    
    return AudioChunk(
        data=data,
        sample_rate=chunk.sample_rate,
        channels=chunk.channels,
        timestamp=chunk.timestamp,
        format=chunk.format
    )

def echo_cancellation_processor(chunk: AudioChunk) -> AudioChunk:
    """Simple echo cancellation processor."""
    # This is a placeholder - real echo cancellation is much more complex
    data = chunk.data.copy()
    
    # Apply simple high-pass filter to reduce low-frequency echo
    if len(data) > 1:
        data[1:] = data[1:] - 0.95 * data[:-1]
    
    return AudioChunk(
        data=data,
        sample_rate=chunk.sample_rate,
        channels=chunk.channels,
        timestamp=chunk.timestamp,
        format=chunk.format
    )

class OptimizedAudioPipeline:
    """Complete optimized audio pipeline."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize processor
        self.processor = StreamingAudioProcessor(
            sample_rate=self.config.get("sample_rate", 16000),
            channels=self.config.get("channels", 1),
            chunk_size=self.config.get("chunk_size", 1024)
        )
        
        # Add default processors
        if self.config.get("noise_reduction", True):
            self.processor.add_processor(noise_reduction_processor)
        
        if self.config.get("volume_normalization", True):
            self.processor.add_processor(volume_normalization_processor)
        
        if self.config.get("echo_cancellation", False):
            self.processor.add_processor(echo_cancellation_processor)
    
    async def start(self) -> None:
        """Start the audio pipeline."""
        self.processor.start_processing()
        logger.info("Optimized audio pipeline started")
    
    async def stop(self) -> None:
        """Stop the audio pipeline."""
        self.processor.stop_processing()
        logger.info("Optimized audio pipeline stopped")
    
    def process_audio(self, audio_data: np.ndarray) -> bool:
        """Process audio data through the pipeline."""
        return self.processor.write_audio(audio_data)
    
    def get_processed_audio(self) -> List[AudioChunk]:
        """Get processed audio chunks."""
        return self.processor.read_audio()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return self.processor.get_stats()


async def main():
    """Test the optimized audio pipeline."""
    logger.info("🎵 TESTING OPTIMIZED AUDIO PIPELINE")
    
    # Create pipeline with configuration
    config = {
        "sample_rate": 16000,
        "channels": 1,
        "chunk_size": 1024,
        "noise_reduction": True,
        "volume_normalization": True,
        "echo_cancellation": False
    }
    
    pipeline = OptimizedAudioPipeline(config)
    
    try:
        # Start pipeline
        await pipeline.start()
        
        # Generate test audio
        duration = 2.0  # seconds
        sample_rate = 16000
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # Create test signal with noise
        signal = np.sin(440 * 2 * np.pi * t) * 0.5  # 440 Hz tone
        noise = np.random.normal(0, 0.05, len(signal))  # Add noise
        test_audio = signal + noise
        
        # Process audio in chunks
        chunk_size = 1024
        for i in range(0, len(test_audio), chunk_size):
            chunk = test_audio[i:i+chunk_size]
            pipeline.process_audio(chunk.astype(np.float32))
            await asyncio.sleep(0.01)  # Simulate real-time processing
        
        # Wait for processing to complete
        await asyncio.sleep(1.0)
        
        # Get processed audio
        processed_chunks = pipeline.get_processed_audio()
        
        # Get performance statistics
        stats = pipeline.get_performance_stats()
        
        print("\n" + "="*50)
        print("🎵 AUDIO PIPELINE TEST RESULTS")
        print("="*50)
        print(f"Processed chunks: {len(processed_chunks)}")
        print(f"Total chunks processed: {stats['processing_stats']['chunks_processed']}")
        print(f"Average latency: {stats['processing_stats']['average_latency']*1000:.2f}ms")
        print(f"Buffer pool reuse rate: {stats['buffer_pool_stats']['reuse_count']}/{stats['buffer_pool_stats']['created_count']}")
        print("="*50)
        
        # Stop pipeline
        await pipeline.stop()
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Audio pipeline test failed: {e}")
        return 1


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    exit(asyncio.run(main()))
