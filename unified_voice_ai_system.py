#!/usr/bin/env python3
"""
UNIFIED VOICE AI SYSTEM
Consolidated architecture bringing together all optimized components
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum
import threading

# Import all our optimized components
from unified_config_system import get_config, UnifiedConfig
from optimized_audio_pipeline import OptimizedAudioPipeline
from comprehensive_error_handler import get_error_handler, resilient_async
from project.audio_memory_patch import get_memory_stats, force_audio_cleanup

# Apply all patches
import project  # noqa: F401  # side-effects: patches applied

logger = logging.getLogger(__name__)

class SystemState(Enum):
    """System state enumeration."""
    INITIALIZING = "initializing"
    READY = "ready"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    SHUTDOWN = "shutdown"

@dataclass
class VoiceInteraction:
    """Voice interaction data structure."""
    session_id: str
    audio_input: bytes
    transcription: str
    llm_response: str
    audio_output: bytes
    start_time: float
    end_time: float
    
    @property
    def total_latency(self) -> float:
        return self.end_time - self.start_time

class UnifiedVoiceAI:
    """Unified Voice AI System with all optimizations."""
    
    def __init__(self, config: Optional[UnifiedConfig] = None):
        self.config = config or get_config()
        self.state = SystemState.INITIALIZING
        self.error_handler = get_error_handler()
        
        # Core components
        self.audio_pipeline: Optional[OptimizedAudioPipeline] = None
        self.stt_engine: Optional[Any] = None
        self.llm_engine: Optional[Any] = None
        self.tts_engine: Optional[Any] = None
        
        # Performance monitoring
        self.performance_stats = {
            "interactions_processed": 0,
            "total_processing_time": 0.0,
            "average_latency": 0.0,
            "stt_latency": 0.0,
            "llm_latency": 0.0,
            "tts_latency": 0.0,
            "error_count": 0,
            "uptime_start": time.time()
        }
        
        # Session management
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self._session_lock = threading.Lock()
        
        # Background tasks
        self._monitoring_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        
        logger.info("Unified Voice AI System initialized")
    
    async def initialize(self) -> bool:
        """Initialize all system components."""
        try:
            logger.info("🚀 Initializing Unified Voice AI System...")
            
            # Initialize audio pipeline
            await self._initialize_audio_pipeline()
            
            # Initialize STT engine
            await self._initialize_stt_engine()
            
            # Initialize LLM engine
            await self._initialize_llm_engine()
            
            # Initialize TTS engine
            await self._initialize_tts_engine()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.state = SystemState.READY
            logger.info("✅ Unified Voice AI System ready")
            return True
            
        except Exception as e:
            self.state = SystemState.ERROR
            await self.error_handler.handle_error(e, "system", {"operation": "initialize"})
            return False
    
    @resilient_async(max_retries=3, backoff_factor=1.0)
    async def _initialize_audio_pipeline(self) -> None:
        """Initialize optimized audio pipeline."""
        logger.info("  🎵 Initializing audio pipeline...")
        
        audio_config = {
            "sample_rate": self.config.audio.sample_rate,
            "channels": self.config.audio.channels,
            "chunk_size": self.config.audio.buffer_size,
            "noise_reduction": True,
            "volume_normalization": True,
            "echo_cancellation": False
        }
        
        self.audio_pipeline = OptimizedAudioPipeline(audio_config)
        await self.audio_pipeline.start()
        
        logger.info("  ✅ Audio pipeline initialized")
    
    @resilient_async(max_retries=3, backoff_factor=1.0)
    async def _initialize_stt_engine(self) -> None:
        """Initialize STT engine."""
        logger.info("  🎤 Initializing STT engine...")
        
        try:
            from faster_whisper import WhisperModel
            
            device = "cpu"
            if self.config.stt.device == "auto":
                try:
                    import torch
                    device = "cuda" if torch.cuda.is_available() else "cpu"
                except ImportError:
                    device = "cpu"
            else:
                device = self.config.stt.device
            
            self.stt_engine = WhisperModel(
                self.config.stt.model,
                device=device,
                compute_type=self.config.stt.compute_type,
                cpu_threads=self.config.performance.stt_cpu_threads,
                num_workers=1
            )
            
            logger.info(f"  ✅ STT engine initialized ({device})")
            
        except Exception as e:
            logger.error(f"  ❌ STT initialization failed: {e}")
            raise
    
    @resilient_async(max_retries=3, backoff_factor=1.0)
    async def _initialize_llm_engine(self) -> None:
        """Initialize LLM engine."""
        logger.info("  🧠 Initializing LLM engine...")
        
        try:
            import ollama
            
            # Test connection
            models = ollama.list()
            if not models.models:
                raise ConnectionError("No Ollama models available")
            
            # Verify target model exists
            model_names = [m.model for m in models.models]
            if self.config.llm.model not in model_names:
                logger.warning(f"Model {self.config.llm.model} not found, using first available: {model_names[0]}")
                self.config.llm.model = model_names[0]
            
            self.llm_engine = ollama
            
            logger.info(f"  ✅ LLM engine initialized ({self.config.llm.model})")
            
        except Exception as e:
            logger.error(f"  ❌ LLM initialization failed: {e}")
            raise
    
    @resilient_async(max_retries=3, backoff_factor=1.0)
    async def _initialize_tts_engine(self) -> None:
        """Initialize TTS engine."""
        logger.info("  🔊 Initializing TTS engine...")
        
        try:
            import edge_tts
            
            # Test TTS with a simple phrase
            test_communicate = edge_tts.Communicate("test", self.config.tts.voice)
            async for chunk in test_communicate.stream():
                if chunk["type"] == "audio":
                    break  # Just test that it works
            
            self.tts_engine = edge_tts
            
            logger.info(f"  ✅ TTS engine initialized ({self.config.tts.voice})")
            
        except Exception as e:
            logger.error(f"  ❌ TTS initialization failed: {e}")
            raise
    
    async def _start_background_tasks(self) -> None:
        """Start background monitoring and cleanup tasks."""
        logger.info("  📊 Starting background tasks...")
        
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info("  ✅ Background tasks started")
    
    async def _monitoring_loop(self) -> None:
        """Background monitoring loop."""
        while self.state not in [SystemState.SHUTDOWN, SystemState.ERROR]:
            try:
                # Update performance stats
                await self._update_performance_stats()
                
                # Check system health
                await self._check_system_health()
                
                # Sleep for monitoring interval
                await asyncio.sleep(self.config.monitoring.interval_seconds)
                
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop."""
        while self.state not in [SystemState.SHUTDOWN, SystemState.ERROR]:
            try:
                # Memory cleanup
                memory_stats = get_memory_stats()
                memory_percent = memory_stats.get("memory_percent", 0)
                
                if memory_percent > self.config.performance.memory_critical_threshold:
                    logger.warning(f"High memory usage: {memory_percent:.1f}%")
                    force_audio_cleanup()
                
                # Session cleanup
                await self._cleanup_old_sessions()
                
                # Sleep for cleanup interval
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
                await asyncio.sleep(300)
    
    async def _update_performance_stats(self) -> None:
        """Update performance statistics."""
        if self.performance_stats["interactions_processed"] > 0:
            self.performance_stats["average_latency"] = (
                self.performance_stats["total_processing_time"] / 
                self.performance_stats["interactions_processed"]
            )
        
        # Get error stats
        error_report = self.error_handler.get_error_report()
        self.performance_stats["error_count"] = error_report["error_stats"]["total_errors"]
    
    async def _check_system_health(self) -> None:
        """Check system health and trigger alerts if needed."""
        memory_stats = get_memory_stats()
        memory_percent = memory_stats.get("memory_percent", 0)
        
        # Check memory usage
        if memory_percent > self.config.monitoring.alert_memory_threshold:
            logger.warning(f"🔥 High memory usage alert: {memory_percent:.1f}%")
        
        # Check average response time
        if self.performance_stats["average_latency"] > self.config.monitoring.alert_response_time_threshold:
            logger.warning(f"⏱️ High latency alert: {self.performance_stats['average_latency']:.2f}s")
    
    async def _cleanup_old_sessions(self) -> None:
        """Clean up old inactive sessions."""
        current_time = time.time()
        sessions_to_remove = []
        
        with self._session_lock:
            for session_id, session_data in self.active_sessions.items():
                last_activity = session_data.get("last_activity", 0)
                if current_time - last_activity > 3600:  # 1 hour timeout
                    sessions_to_remove.append(session_id)
            
            for session_id in sessions_to_remove:
                del self.active_sessions[session_id]
        
        if sessions_to_remove:
            logger.info(f"Cleaned up {len(sessions_to_remove)} inactive sessions")
    
    async def process_voice_interaction(self, session_id: str, audio_data: bytes) -> Optional[VoiceInteraction]:
        """Process a complete voice interaction."""
        if self.state != SystemState.READY:
            logger.warning(f"System not ready for processing (state: {self.state})")
            return None
        
        start_time = time.time()
        interaction = VoiceInteraction(
            session_id=session_id,
            audio_input=audio_data,
            transcription="",
            llm_response="",
            audio_output=b"",
            start_time=start_time,
            end_time=0.0
        )
        
        try:
            self.state = SystemState.RUNNING
            
            # Update session
            with self._session_lock:
                if session_id not in self.active_sessions:
                    self.active_sessions[session_id] = {"created": time.time()}
                self.active_sessions[session_id]["last_activity"] = time.time()
            
            # Step 1: Speech-to-Text
            stt_start = time.time()
            interaction.transcription = await self._process_stt(audio_data)
            stt_time = time.time() - stt_start
            self.performance_stats["stt_latency"] = stt_time
            
            if not interaction.transcription:
                logger.warning("No transcription generated")
                return interaction
            
            # Step 2: LLM Processing
            llm_start = time.time()
            interaction.llm_response = await self._process_llm(interaction.transcription, session_id)
            llm_time = time.time() - llm_start
            self.performance_stats["llm_latency"] = llm_time
            
            if not interaction.llm_response:
                logger.warning("No LLM response generated")
                return interaction
            
            # Step 3: Text-to-Speech
            tts_start = time.time()
            interaction.audio_output = await self._process_tts(interaction.llm_response)
            tts_time = time.time() - tts_start
            self.performance_stats["tts_latency"] = tts_time
            
            # Update interaction timing
            interaction.end_time = time.time()
            
            # Update performance stats
            self.performance_stats["interactions_processed"] += 1
            self.performance_stats["total_processing_time"] += interaction.total_latency
            
            logger.info(f"Voice interaction completed in {interaction.total_latency:.2f}s "
                       f"(STT: {stt_time:.2f}s, LLM: {llm_time:.2f}s, TTS: {tts_time:.2f}s)")
            
            return interaction
            
        except Exception as e:
            await self.error_handler.handle_error(e, "voice_interaction", {"session_id": session_id})
            interaction.end_time = time.time()
            return interaction
        
        finally:
            self.state = SystemState.READY
    
    async def _process_stt(self, audio_data: bytes) -> str:
        """Process speech-to-text."""
        try:
            # Convert audio data to numpy array (simplified)
            import numpy as np
            audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            
            # Transcribe
            segments, _ = self.stt_engine.transcribe(
                audio_array,
                language=self.config.stt.language,
                beam_size=self.config.stt.beam_size,
                temperature=self.config.stt.temperature
            )
            
            transcription = " ".join([segment.text for segment in segments])
            return transcription.strip()
            
        except Exception as e:
            await self.error_handler.handle_error(e, "stt")
            return ""
    
    async def _process_llm(self, text: str, session_id: str) -> str:
        """Process LLM response."""
        try:
            # Get conversation context
            context = self.active_sessions.get(session_id, {}).get("context", [])
            
            # Build messages
            messages = [{"role": "system", "content": self.config.llm.system_prompt}]
            messages.extend(context)
            messages.append({"role": "user", "content": text})
            
            # Generate response
            response = self.llm_engine.chat(
                model=self.config.llm.model,
                messages=messages,
                options={
                    "temperature": self.config.llm.temperature,
                    "top_p": self.config.llm.top_p,
                    "top_k": self.config.llm.top_k,
                    "num_predict": self.config.llm.max_tokens
                }
            )
            
            llm_response = response["message"]["content"]
            
            # Update conversation context
            with self._session_lock:
                if "context" not in self.active_sessions[session_id]:
                    self.active_sessions[session_id]["context"] = []
                
                context = self.active_sessions[session_id]["context"]
                context.append({"role": "user", "content": text})
                context.append({"role": "assistant", "content": llm_response})
                
                # Keep only recent messages
                if len(context) > self.config.performance.max_chat_history:
                    context = context[-self.config.performance.max_chat_history:]
                    self.active_sessions[session_id]["context"] = context
            
            return llm_response
            
        except Exception as e:
            await self.error_handler.handle_error(e, "llm")
            return ""
    
    async def _process_tts(self, text: str) -> bytes:
        """Process text-to-speech."""
        try:
            communicate = self.tts_engine.Communicate(text, self.config.tts.voice)
            
            audio_chunks = []
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_chunks.append(chunk["data"])
            
            return b"".join(audio_chunks)
            
        except Exception as e:
            await self.error_handler.handle_error(e, "tts")
            return b""
    
    async def shutdown(self) -> None:
        """Shutdown the system gracefully."""
        logger.info("🛑 Shutting down Unified Voice AI System...")
        
        self.state = SystemState.SHUTDOWN
        
        # Cancel background tasks
        if self._monitoring_task:
            self._monitoring_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        # Shutdown components
        if self.audio_pipeline:
            await self.audio_pipeline.stop()
        
        logger.info("✅ Unified Voice AI System shutdown complete")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        memory_stats = get_memory_stats()
        error_report = self.error_handler.get_error_report()
        
        return {
            "state": self.state.value,
            "uptime": time.time() - self.performance_stats["uptime_start"],
            "performance_stats": self.performance_stats.copy(),
            "memory_stats": memory_stats,
            "error_stats": error_report,
            "active_sessions": len(self.active_sessions),
            "config_summary": {
                "stt_model": self.config.stt.model,
                "llm_model": self.config.llm.model,
                "tts_voice": self.config.tts.voice,
                "environment": self.config.environment
            }
        }


# Global system instance
_voice_ai_system: Optional[UnifiedVoiceAI] = None

async def get_voice_ai_system() -> UnifiedVoiceAI:
    """Get the global voice AI system instance."""
    global _voice_ai_system
    if _voice_ai_system is None:
        _voice_ai_system = UnifiedVoiceAI()
        await _voice_ai_system.initialize()
    return _voice_ai_system


async def main():
    """Test the unified voice AI system."""
    logging.basicConfig(level=logging.INFO)
    
    logger.info("🎯 TESTING UNIFIED VOICE AI SYSTEM")
    
    try:
        # Initialize system
        system = await get_voice_ai_system()
        
        # Get system status
        status = system.get_system_status()
        
        print("\n" + "="*60)
        print("🎯 UNIFIED VOICE AI SYSTEM STATUS")
        print("="*60)
        print(f"State: {status['state']}")
        print(f"Uptime: {status['uptime']:.1f}s")
        print(f"STT Model: {status['config_summary']['stt_model']}")
        print(f"LLM Model: {status['config_summary']['llm_model']}")
        print(f"TTS Voice: {status['config_summary']['tts_voice']}")
        print(f"Memory Usage: {status['memory_stats'].get('memory_percent', 0):.1f}%")
        print(f"Active Sessions: {status['active_sessions']}")
        print("="*60)
        
        # Test voice interaction (simulated)
        logger.info("Testing voice interaction...")

        try:
            # Simulate audio input
            import numpy as np
            test_audio = np.random.normal(0, 0.1, 16000).astype(np.int16).tobytes()

            # Process interaction
            interaction = await system.process_voice_interaction("test_session", test_audio)

            if interaction:
                print(f"\n🎤 Voice Interaction Test:")
                print(f"  Latency: {interaction.total_latency:.2f}s")
                print(f"  Transcription: {interaction.transcription or 'None'}")
                print(f"  Response: {interaction.llm_response or 'None'}")
                print(f"  Audio Output: {len(interaction.audio_output)} bytes")
            else:
                print("\n⚠️ Voice interaction test failed")

        except Exception as e:
            logger.warning(f"Voice interaction test failed: {e}")
            print("\n⚠️ Voice interaction test skipped due to missing dependencies")
        
        # Shutdown
        await system.shutdown()
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ System test failed: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
