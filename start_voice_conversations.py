#!/usr/bin/env python3
"""
🎤 START VOICE CONVERSATIONS
Simple script to start voice conversations with AI using existing optimized system
"""

import asyncio
import logging
import time
import sys
import os
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("voice-conversations")

def print_banner():
    """Print startup banner"""
    print("🎤 VOICE CONVERSATIONS WITH AI")
    print("=" * 50)
    print("🚀 Starting optimized voice system...")
    print("💬 Get ready for voice conversations!")
    print("=" * 50)

def check_system_status():
    """Check if the voice system is ready"""
    print("🔍 Checking system status...")
    
    # Check if optimizations are available
    try:
        # Import the optimized system
        import project  # This applies all patches
        print("✅ Voice optimizations loaded")
        
        # Check STT
        try:
            import faster_whisper
            print("✅ STT engine available (faster-whisper)")
        except ImportError:
            print("⚠️ STT engine fallback mode")
        
        # Check TTS
        try:
            import edge_tts
            print("✅ TTS engine available (edge-tts)")
        except ImportError:
            print("⚠️ TTS engine fallback mode")
        
        # Check LLM
        try:
            import ollama
            models = ollama.list()
            if models.get('models'):
                available_models = [m['name'] for m in models['models']]
                print(f"✅ LLM available - Models: {available_models[:2]}")
            else:
                print("⚠️ LLM fallback mode (no models)")
        except Exception:
            print("⚠️ LLM fallback mode")
        
        return True
        
    except Exception as e:
        print(f"❌ System check failed: {e}")
        return False

def run_voice_test():
    """Run a quick voice system test"""
    print("\n🧪 Running voice system test...")
    
    try:
        # Run the local voice test
        import subprocess
        result = subprocess.run([
            sys.executable, "run_local_voice_test.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Voice system test passed")
            return True
        else:
            print("⚠️ Voice system test had issues")
            print(f"Output: {result.stdout[-200:]}")  # Last 200 chars
            return False
            
    except Exception as e:
        print(f"❌ Voice test failed: {e}")
        return False

def start_simple_voice_chat():
    """Start a simple voice chat using existing components"""
    print("\n🎙️ STARTING SIMPLE VOICE CHAT")
    print("-" * 40)
    print("💡 Instructions:")
    print("  1. Speak clearly into your microphone")
    print("  2. Wait for the AI response")
    print("  3. Say 'exit' or 'quit' to stop")
    print("  4. Press Ctrl+C to force exit")
    print("-" * 40)
    
    try:
        # Try to use the unified voice system
        import subprocess
        result = subprocess.run([
            sys.executable, "unified_voice_ai_system.py"
        ], timeout=300)  # 5 minute timeout
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n👋 Voice chat ended by user")
        return True
    except subprocess.TimeoutExpired:
        print("\n⏰ Voice chat timed out")
        return False
    except Exception as e:
        print(f"\n❌ Voice chat failed: {e}")
        return False

def start_voice_master():
    """Start the VoiceMaster system"""
    print("\n🎤 STARTING VOICE MASTER SYSTEM")
    print("-" * 40)
    
    try:
        # Try to use VoiceMaster
        import subprocess
        result = subprocess.run([
            sys.executable, "VoiceMaster.py"
        ], timeout=300)  # 5 minute timeout
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n👋 VoiceMaster ended by user")
        return True
    except subprocess.TimeoutExpired:
        print("\n⏰ VoiceMaster timed out")
        return False
    except Exception as e:
        print(f"\n❌ VoiceMaster failed: {e}")
        return False

def start_optimized_voice_agent():
    """Start the optimized voice agent"""
    print("\n🚀 STARTING OPTIMIZED VOICE AGENT")
    print("-" * 40)
    
    try:
        # Try to use the optimized agent
        import subprocess
        result = subprocess.run([
            sys.executable, "run_optimized_voice_agent.py"
        ], timeout=300)  # 5 minute timeout
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n👋 Optimized agent ended by user")
        return True
    except subprocess.TimeoutExpired:
        print("\n⏰ Optimized agent timed out")
        return False
    except Exception as e:
        print(f"\n❌ Optimized agent failed: {e}")
        return False

def show_menu():
    """Show voice system menu"""
    print("\n🎯 VOICE SYSTEM MENU")
    print("=" * 30)
    print("1. 🧪 Run voice system test")
    print("2. 🎙️ Start simple voice chat")
    print("3. 🎤 Start VoiceMaster system")
    print("4. 🚀 Start optimized voice agent")
    print("5. 📊 Check system status")
    print("6. ❌ Exit")
    print("=" * 30)
    
    while True:
        try:
            choice = input("👉 Choose an option (1-6): ").strip()
            
            if choice == "1":
                run_voice_test()
            elif choice == "2":
                start_simple_voice_chat()
            elif choice == "3":
                start_voice_master()
            elif choice == "4":
                start_optimized_voice_agent()
            elif choice == "5":
                check_system_status()
            elif choice == "6":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please choose 1-6.")
                
        except KeyboardInterrupt:
            print("\n👋 Exiting...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def auto_start_best_system():
    """Automatically start the best available voice system"""
    print("\n🤖 AUTO-STARTING BEST VOICE SYSTEM")
    print("-" * 40)
    
    # Try systems in order of preference
    systems = [
        ("Optimized Voice Agent", start_optimized_voice_agent),
        ("Simple Voice Chat", start_simple_voice_chat),
        ("VoiceMaster System", start_voice_master)
    ]
    
    for system_name, start_func in systems:
        print(f"🔄 Trying {system_name}...")
        try:
            if start_func():
                print(f"✅ {system_name} completed successfully")
                return True
        except Exception as e:
            print(f"❌ {system_name} failed: {e}")
            continue
    
    print("❌ All voice systems failed to start")
    return False

def main():
    """Main function"""
    print_banner()
    
    # Check system status
    if not check_system_status():
        print("\n❌ System not ready. Please run dependency fixes first.")
        print("🔧 Try: python fix_voice_dependencies.py")
        return 1
    
    print("\n✅ System ready for voice conversations!")
    
    # Ask user preference
    print("\n🎯 How would you like to start?")
    print("1. 🤖 Auto-start best system")
    print("2. 📋 Show menu for manual selection")
    
    try:
        choice = input("👉 Choose (1 or 2): ").strip()
        
        if choice == "1":
            success = auto_start_best_system()
            return 0 if success else 1
        elif choice == "2":
            show_menu()
            return 0
        else:
            print("❌ Invalid choice. Starting auto mode...")
            success = auto_start_best_system()
            return 0 if success else 1
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        return 0
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
