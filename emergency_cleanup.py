#!/usr/bin/env python3
"""
EMERGENCY SYSTEM CLEANUP
Aggressive cleanup to resolve disk space crisis and system optimization
"""

import os
import shutil
import glob
import logging
import time
import psutil
from pathlib import Path
from typing import List, Tuple

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("emergency_cleanup")

class EmergencyCleanup:
    """Aggressive system cleanup for emergency situations."""
    
    def __init__(self, workspace_root: str = "."):
        self.workspace_root = Path(workspace_root).resolve()
        self.cleaned_size = 0
        self.cleaned_files = 0
        
    def get_disk_usage(self) -> Tuple[float, float, float]:
        """Get current disk usage statistics."""
        usage = shutil.disk_usage(self.workspace_root)
        total_gb = usage.total / (1024**3)
        used_gb = usage.used / (1024**3)
        free_gb = usage.free / (1024**3)
        return total_gb, used_gb, free_gb
    
    def clean_python_cache(self) -> int:
        """Remove all Python cache files and directories."""
        logger.info("🧹 Cleaning Python cache files...")
        cleaned = 0
        
        # Find all __pycache__ directories
        for pycache_dir in self.workspace_root.rglob("__pycache__"):
            try:
                size = sum(f.stat().st_size for f in pycache_dir.rglob("*") if f.is_file())
                shutil.rmtree(pycache_dir)
                cleaned += size
                self.cleaned_files += 1
                logger.info(f"   Removed {pycache_dir} ({size / 1024:.1f} KB)")
            except Exception as e:
                logger.warning(f"   Failed to remove {pycache_dir}: {e}")
        
        # Find all .pyc files
        for pyc_file in self.workspace_root.rglob("*.pyc"):
            try:
                size = pyc_file.stat().st_size
                pyc_file.unlink()
                cleaned += size
                self.cleaned_files += 1
            except Exception as e:
                logger.warning(f"   Failed to remove {pyc_file}: {e}")
        
        return cleaned
    
    def clean_logs_and_temp(self) -> int:
        """Remove log files and temporary files."""
        logger.info("🧹 Cleaning logs and temporary files...")
        cleaned = 0
        
        patterns = [
            "*.log", "*.tmp", "*.temp", "*.cache",
            "*.bak", "*.backup", "*.old",
            "temp_*", "tmp_*", "*_temp", "*_tmp"
        ]
        
        for pattern in patterns:
            for file_path in self.workspace_root.rglob(pattern):
                if file_path.is_file():
                    try:
                        size = file_path.stat().st_size
                        if size > 1024:  # Only remove files > 1KB
                            file_path.unlink()
                            cleaned += size
                            self.cleaned_files += 1
                            logger.info(f"   Removed {file_path.name} ({size / 1024:.1f} KB)")
                    except Exception as e:
                        logger.warning(f"   Failed to remove {file_path}: {e}")
        
        return cleaned
    
    def clean_duplicate_models(self) -> int:
        """Remove duplicate model files and large binaries."""
        logger.info("🧹 Cleaning duplicate models and large files...")
        cleaned = 0
        
        # Look for large files that might be duplicates
        large_files = []
        for file_path in self.workspace_root.rglob("*"):
            if file_path.is_file():
                try:
                    size = file_path.stat().st_size
                    if size > 100 * 1024 * 1024:  # Files > 100MB
                        large_files.append((file_path, size))
                except Exception:
                    continue
        
        # Group by size to find potential duplicates
        size_groups = {}
        for file_path, size in large_files:
            if size not in size_groups:
                size_groups[size] = []
            size_groups[size].append(file_path)
        
        # Report large files for manual review
        for size, files in size_groups.items():
            if len(files) > 1:
                logger.warning(f"   Potential duplicates ({size / 1024**2:.1f} MB each):")
                for file_path in files:
                    logger.warning(f"     - {file_path}")
        
        return cleaned
    
    def clean_test_artifacts(self) -> int:
        """Remove test artifacts and generated files."""
        logger.info("🧹 Cleaning test artifacts...")
        cleaned = 0
        
        patterns = [
            "test_results.json", "performance_report.json",
            "*.wav", "*.mp3", "*.m4a",  # Audio test files
            "coverage.xml", "pytest_cache",
            ".pytest_cache", ".coverage"
        ]
        
        for pattern in patterns:
            for file_path in self.workspace_root.rglob(pattern):
                try:
                    if file_path.is_file():
                        size = file_path.stat().st_size
                        file_path.unlink()
                        cleaned += size
                        self.cleaned_files += 1
                    elif file_path.is_dir():
                        size = sum(f.stat().st_size for f in file_path.rglob("*") if f.is_file())
                        shutil.rmtree(file_path)
                        cleaned += size
                        self.cleaned_files += 1
                except Exception as e:
                    logger.warning(f"   Failed to remove {file_path}: {e}")
        
        return cleaned
    
    def optimize_git_repo(self) -> int:
        """Optimize git repository if present."""
        logger.info("🧹 Optimizing git repository...")
        cleaned = 0
        
        git_dir = self.workspace_root / ".git"
        if git_dir.exists():
            try:
                # Run git gc to cleanup
                import subprocess
                result = subprocess.run(
                    ["git", "gc", "--aggressive", "--prune=now"],
                    cwd=self.workspace_root,
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    logger.info("   Git repository optimized")
                else:
                    logger.warning(f"   Git optimization failed: {result.stderr}")
            except Exception as e:
                logger.warning(f"   Git optimization error: {e}")
        
        return cleaned
    
    def run_emergency_cleanup(self) -> None:
        """Run complete emergency cleanup."""
        logger.info("🚨 EMERGENCY CLEANUP STARTING")
        logger.info("=" * 50)
        
        # Get initial disk usage
        total_gb, used_gb, free_gb = self.get_disk_usage()
        initial_usage_pct = (used_gb / total_gb) * 100
        
        logger.info(f"📊 Initial disk usage: {used_gb:.1f}GB / {total_gb:.1f}GB ({initial_usage_pct:.1f}%)")
        logger.info(f"📊 Free space: {free_gb:.1f}GB")
        
        start_time = time.time()
        
        # Run cleanup operations
        self.cleaned_size += self.clean_python_cache()
        self.cleaned_size += self.clean_logs_and_temp()
        self.cleaned_size += self.clean_duplicate_models()
        self.cleaned_size += self.clean_test_artifacts()
        self.cleaned_size += self.optimize_git_repo()
        
        # Get final disk usage
        total_gb, used_gb, free_gb = self.get_disk_usage()
        final_usage_pct = (used_gb / total_gb) * 100
        
        cleanup_time = time.time() - start_time
        
        logger.info("=" * 50)
        logger.info("✅ EMERGENCY CLEANUP COMPLETE")
        logger.info(f"📊 Final disk usage: {used_gb:.1f}GB / {total_gb:.1f}GB ({final_usage_pct:.1f}%)")
        logger.info(f"📊 Free space: {free_gb:.1f}GB")
        logger.info(f"🧹 Cleaned: {self.cleaned_size / 1024**2:.1f}MB in {self.cleaned_files} files")
        logger.info(f"⏱️  Cleanup time: {cleanup_time:.1f}s")
        
        improvement = initial_usage_pct - final_usage_pct
        logger.info(f"📈 Disk usage improvement: {improvement:.1f}%")
        
        if final_usage_pct > 85:
            logger.warning("⚠️  Disk usage still high - manual intervention may be needed")
        else:
            logger.info("✅ Disk usage now in acceptable range")


def main():
    """Run emergency cleanup."""
    cleanup = EmergencyCleanup()
    cleanup.run_emergency_cleanup()
    return 0


if __name__ == "__main__":
    exit(main())
