#!/usr/bin/env python3
"""
STT PERFORMANCE BENCHMARK
Comprehensive benchmarking and optimization testing for Speech-to-Text performance
"""

import asyncio
import logging
import time
import json
import statistics
from typing import Dict, Any, List, Tuple
import numpy as np
from pathlib import Path

# Import optimized components
from advanced_stt_optimizer import AdvancedSTTEngine, STTPerformanceConfig, STTModel, STTDevice

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("stt_benchmark")

class STTBenchmark:
    """Comprehensive STT performance benchmarking system."""
    
    def __init__(self):
        self.results = {}
        self.test_audio_cache = {}
        
    async def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """Run complete STT performance benchmark."""
        logger.info("🎤 STARTING COMPREHENSIVE STT BENCHMARK")
        logger.info("=" * 60)
        
        benchmark_start = time.time()
        
        # Test 1: Model Performance Comparison
        logger.info("📊 Test 1: Model Performance Comparison")
        self.results["model_comparison"] = await self._benchmark_models()
        
        # Test 2: Device Performance Comparison
        logger.info("⚡ Test 2: Device Performance Comparison")
        self.results["device_comparison"] = await self._benchmark_devices()
        
        # Test 3: Audio Length Scaling
        logger.info("📏 Test 3: Audio Length Scaling Performance")
        self.results["length_scaling"] = await self._benchmark_audio_lengths()
        
        # Test 4: Concurrent Processing
        logger.info("🔄 Test 4: Concurrent Processing Performance")
        self.results["concurrent_processing"] = await self._benchmark_concurrent()
        
        # Test 5: Memory Usage Analysis
        logger.info("💾 Test 5: Memory Usage Analysis")
        self.results["memory_analysis"] = await self._benchmark_memory()
        
        # Test 6: Real-time Performance
        logger.info("⏱️ Test 6: Real-time Performance Simulation")
        self.results["realtime_performance"] = await self._benchmark_realtime()
        
        # Test 7: Optimization Impact
        logger.info("🚀 Test 7: Optimization Impact Analysis")
        self.results["optimization_impact"] = await self._benchmark_optimizations()
        
        total_time = time.time() - benchmark_start
        self.results["benchmark_summary"] = {
            "total_time": total_time,
            "timestamp": time.time(),
            "tests_completed": 7
        }
        
        logger.info("=" * 60)
        logger.info(f"✅ BENCHMARK COMPLETE ({total_time:.1f}s)")
        
        return self.results
    
    def _generate_test_audio(self, duration: float, audio_type: str = "speech") -> np.ndarray:
        """Generate test audio of specified duration and type."""
        cache_key = f"{duration}_{audio_type}"
        if cache_key in self.test_audio_cache:
            return self.test_audio_cache[cache_key]
        
        sample_rate = 16000
        samples = int(duration * sample_rate)
        
        if audio_type == "speech":
            # Generate speech-like signal with multiple frequencies
            t = np.linspace(0, duration, samples, False)
            signal = (
                0.3 * np.sin(2 * np.pi * 440 * t) +  # Base frequency
                0.2 * np.sin(2 * np.pi * 880 * t) +  # Harmonic
                0.1 * np.sin(2 * np.pi * 1320 * t) + # Higher harmonic
                0.05 * np.random.normal(0, 1, samples)  # Noise
            )
            # Apply envelope to simulate speech patterns
            envelope = np.abs(np.sin(2 * np.pi * 2 * t))  # 2 Hz modulation
            signal = signal * envelope
            
        elif audio_type == "noise":
            # Generate noise
            signal = np.random.normal(0, 0.1, samples)
            
        elif audio_type == "silence":
            # Generate silence
            signal = np.zeros(samples)
            
        else:
            # Default to sine wave
            t = np.linspace(0, duration, samples, False)
            signal = 0.3 * np.sin(2 * np.pi * 440 * t)
        
        audio = signal.astype(np.float32)
        self.test_audio_cache[cache_key] = audio
        return audio
    
    async def _benchmark_models(self) -> Dict[str, Any]:
        """Benchmark different STT models."""
        results = {}
        test_audio = self._generate_test_audio(2.0, "speech")  # 2 seconds of speech-like audio
        
        models_to_test = [STTModel.TINY_EN, STTModel.BASE_EN, STTModel.SMALL_EN]
        
        for model in models_to_test:
            logger.info(f"  Testing model: {model.value}")
            
            try:
                config = STTPerformanceConfig()
                config.model = model
                config.device = STTDevice.AUTO
                
                engine = AdvancedSTTEngine(config)
                await engine.initialize()
                
                # Run multiple tests
                rtf_values = []
                processing_times = []
                
                for i in range(5):
                    start_time = time.time()
                    text, rtf = await engine.transcribe(test_audio)
                    processing_time = time.time() - start_time
                    
                    rtf_values.append(rtf)
                    processing_times.append(processing_time)
                
                await engine.shutdown()
                
                results[model.value] = {
                    "avg_rtf": statistics.mean(rtf_values),
                    "min_rtf": min(rtf_values),
                    "max_rtf": max(rtf_values),
                    "std_rtf": statistics.stdev(rtf_values) if len(rtf_values) > 1 else 0,
                    "avg_processing_time": statistics.mean(processing_times),
                    "tests_run": len(rtf_values)
                }
                
                logger.info(f"    Avg RTF: {results[model.value]['avg_rtf']:.3f}")
                
            except Exception as e:
                logger.error(f"    Model {model.value} test failed: {e}")
                results[model.value] = {"error": str(e)}
        
        return results
    
    async def _benchmark_devices(self) -> Dict[str, Any]:
        """Benchmark CPU vs GPU performance."""
        results = {}
        test_audio = self._generate_test_audio(3.0, "speech")
        
        devices_to_test = [STTDevice.CPU, STTDevice.CUDA]
        
        for device in devices_to_test:
            logger.info(f"  Testing device: {device.value}")
            
            try:
                config = STTPerformanceConfig()
                config.model = STTModel.TINY_EN
                config.device = device
                
                engine = AdvancedSTTEngine(config)
                success = await engine.initialize()
                
                if not success:
                    results[device.value] = {"error": "Initialization failed"}
                    continue
                
                # Warmup
                await engine.transcribe(test_audio[:8000])  # 0.5s warmup
                
                # Benchmark
                rtf_values = []
                for i in range(10):
                    text, rtf = await engine.transcribe(test_audio)
                    rtf_values.append(rtf)
                
                await engine.shutdown()
                
                results[device.value] = {
                    "avg_rtf": statistics.mean(rtf_values),
                    "min_rtf": min(rtf_values),
                    "max_rtf": max(rtf_values),
                    "improvement_vs_cpu": 0.0  # Will be calculated later
                }
                
                logger.info(f"    Avg RTF: {results[device.value]['avg_rtf']:.3f}")
                
            except Exception as e:
                logger.error(f"    Device {device.value} test failed: {e}")
                results[device.value] = {"error": str(e)}
        
        # Calculate improvement
        if "cpu" in results and "cuda" in results:
            if "error" not in results["cpu"] and "error" not in results["cuda"]:
                cpu_rtf = results["cpu"]["avg_rtf"]
                gpu_rtf = results["cuda"]["avg_rtf"]
                improvement = ((cpu_rtf - gpu_rtf) / cpu_rtf) * 100
                results["cuda"]["improvement_vs_cpu"] = improvement
        
        return results
    
    async def _benchmark_audio_lengths(self) -> Dict[str, Any]:
        """Benchmark performance across different audio lengths."""
        results = {}
        durations = [0.5, 1.0, 2.0, 5.0, 10.0, 30.0]  # seconds
        
        config = STTPerformanceConfig()
        config.model = STTModel.TINY_EN
        config.device = STTDevice.AUTO
        
        engine = AdvancedSTTEngine(config)
        await engine.initialize()
        
        for duration in durations:
            logger.info(f"  Testing {duration}s audio")
            
            test_audio = self._generate_test_audio(duration, "speech")
            
            rtf_values = []
            for i in range(3):  # 3 tests per duration
                text, rtf = await engine.transcribe(test_audio)
                rtf_values.append(rtf)
            
            results[f"{duration}s"] = {
                "duration": duration,
                "avg_rtf": statistics.mean(rtf_values),
                "min_rtf": min(rtf_values),
                "max_rtf": max(rtf_values)
            }
            
            logger.info(f"    Avg RTF: {results[f'{duration}s']['avg_rtf']:.3f}")
        
        await engine.shutdown()
        return results
    
    async def _benchmark_concurrent(self) -> Dict[str, Any]:
        """Benchmark concurrent processing performance."""
        results = {}
        test_audio = self._generate_test_audio(2.0, "speech")
        
        config = STTPerformanceConfig()
        config.model = STTModel.TINY_EN
        config.device = STTDevice.AUTO
        
        # Test different concurrency levels
        concurrency_levels = [1, 2, 4, 8]
        
        for concurrency in concurrency_levels:
            logger.info(f"  Testing {concurrency} concurrent requests")
            
            try:
                engines = []
                for i in range(concurrency):
                    engine = AdvancedSTTEngine(config)
                    await engine.initialize()
                    engines.append(engine)
                
                # Run concurrent transcriptions
                start_time = time.time()
                
                tasks = []
                for engine in engines:
                    task = asyncio.create_task(engine.transcribe(test_audio))
                    tasks.append(task)
                
                results_list = await asyncio.gather(*tasks)
                
                total_time = time.time() - start_time
                
                # Calculate metrics
                rtf_values = [result[1] for result in results_list]
                
                results[f"concurrent_{concurrency}"] = {
                    "concurrency": concurrency,
                    "total_time": total_time,
                    "avg_rtf": statistics.mean(rtf_values),
                    "throughput": concurrency / total_time  # requests per second
                }
                
                # Cleanup
                for engine in engines:
                    await engine.shutdown()
                
                logger.info(f"    Throughput: {results[f'concurrent_{concurrency}']['throughput']:.2f} req/s")
                
            except Exception as e:
                logger.error(f"    Concurrency {concurrency} test failed: {e}")
                results[f"concurrent_{concurrency}"] = {"error": str(e)}
        
        return results
    
    async def _benchmark_memory(self) -> Dict[str, Any]:
        """Benchmark memory usage patterns."""
        results = {}
        
        try:
            import psutil
            process = psutil.Process()
            
            # Baseline memory
            baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            config = STTPerformanceConfig()
            config.model = STTModel.TINY_EN
            
            engine = AdvancedSTTEngine(config)
            
            # Memory after initialization
            await engine.initialize()
            init_memory = process.memory_info().rss / 1024 / 1024
            
            # Memory during processing
            test_audio = self._generate_test_audio(5.0, "speech")
            
            memory_samples = []
            for i in range(10):
                await engine.transcribe(test_audio)
                memory_samples.append(process.memory_info().rss / 1024 / 1024)
            
            await engine.shutdown()
            final_memory = process.memory_info().rss / 1024 / 1024
            
            results = {
                "baseline_memory_mb": baseline_memory,
                "init_memory_mb": init_memory,
                "avg_processing_memory_mb": statistics.mean(memory_samples),
                "max_processing_memory_mb": max(memory_samples),
                "final_memory_mb": final_memory,
                "memory_overhead_mb": init_memory - baseline_memory,
                "memory_growth_mb": max(memory_samples) - init_memory
            }
            
            logger.info(f"  Memory overhead: {results['memory_overhead_mb']:.1f}MB")
            logger.info(f"  Memory growth: {results['memory_growth_mb']:.1f}MB")
            
        except ImportError:
            results = {"error": "psutil not available"}
        except Exception as e:
            results = {"error": str(e)}
        
        return results
    
    async def _benchmark_realtime(self) -> Dict[str, Any]:
        """Benchmark real-time performance simulation."""
        results = {}
        
        config = STTPerformanceConfig()
        config.model = STTModel.TINY_EN
        
        engine = AdvancedSTTEngine(config)
        await engine.initialize()
        
        # Simulate real-time audio chunks
        chunk_duration = 1.0  # 1 second chunks
        total_duration = 10.0  # 10 seconds total
        
        rtf_values = []
        latencies = []
        
        for i in range(int(total_duration / chunk_duration)):
            chunk_audio = self._generate_test_audio(chunk_duration, "speech")
            
            start_time = time.time()
            text, rtf = await engine.transcribe(chunk_audio)
            latency = time.time() - start_time
            
            rtf_values.append(rtf)
            latencies.append(latency)
            
            # Simulate real-time constraint
            if latency > chunk_duration:
                logger.warning(f"  Real-time constraint violated: {latency:.3f}s > {chunk_duration}s")
        
        await engine.shutdown()
        
        results = {
            "avg_rtf": statistics.mean(rtf_values),
            "avg_latency": statistics.mean(latencies),
            "max_latency": max(latencies),
            "realtime_violations": sum(1 for lat in latencies if lat > chunk_duration),
            "realtime_performance": sum(1 for lat in latencies if lat <= chunk_duration) / len(latencies)
        }
        
        logger.info(f"  Real-time performance: {results['realtime_performance']:.2%}")
        
        return results
    
    async def _benchmark_optimizations(self) -> Dict[str, Any]:
        """Benchmark impact of different optimizations."""
        results = {}
        test_audio = self._generate_test_audio(3.0, "speech")
        
        # Test configurations
        configs = {
            "baseline": {
                "model": STTModel.BASE_EN,
                "beam_size": 5,
                "enable_audio_enhancement": False,
                "vad_filter": False
            },
            "speed_optimized": {
                "model": STTModel.TINY_EN,
                "beam_size": 1,
                "enable_audio_enhancement": True,
                "vad_filter": True
            },
            "accuracy_optimized": {
                "model": STTModel.SMALL_EN,
                "beam_size": 5,
                "enable_audio_enhancement": True,
                "vad_filter": True
            }
        }
        
        for config_name, config_params in configs.items():
            logger.info(f"  Testing {config_name} configuration")
            
            try:
                config = STTPerformanceConfig()
                config.model = config_params["model"]
                config.beam_size = config_params["beam_size"]
                config.enable_audio_enhancement = config_params["enable_audio_enhancement"]
                config.vad_filter = config_params["vad_filter"]
                
                engine = AdvancedSTTEngine(config)
                await engine.initialize()
                
                rtf_values = []
                for i in range(5):
                    text, rtf = await engine.transcribe(test_audio)
                    rtf_values.append(rtf)
                
                await engine.shutdown()
                
                results[config_name] = {
                    "avg_rtf": statistics.mean(rtf_values),
                    "min_rtf": min(rtf_values),
                    "max_rtf": max(rtf_values),
                    "config": config_params
                }
                
                logger.info(f"    Avg RTF: {results[config_name]['avg_rtf']:.3f}")
                
            except Exception as e:
                logger.error(f"    Configuration {config_name} test failed: {e}")
                results[config_name] = {"error": str(e)}
        
        return results
    
    def save_results(self, filename: str = "stt_benchmark_results.json") -> None:
        """Save benchmark results to file."""
        try:
            with open(filename, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            logger.info(f"✅ Benchmark results saved to {filename}")
        except Exception as e:
            logger.error(f"❌ Failed to save results: {e}")
    
    def print_summary(self) -> None:
        """Print benchmark summary."""
        print("\n" + "="*60)
        print("🎤 STT PERFORMANCE BENCHMARK SUMMARY")
        print("="*60)
        
        # Model comparison
        if "model_comparison" in self.results:
            print("\n📊 Model Performance:")
            for model, data in self.results["model_comparison"].items():
                if "error" not in data:
                    print(f"  {model}: RTF {data['avg_rtf']:.3f} (±{data['std_rtf']:.3f})")
        
        # Device comparison
        if "device_comparison" in self.results:
            print("\n⚡ Device Performance:")
            for device, data in self.results["device_comparison"].items():
                if "error" not in data:
                    improvement = f" ({data['improvement_vs_cpu']:+.1f}%)" if data['improvement_vs_cpu'] != 0 else ""
                    print(f"  {device}: RTF {data['avg_rtf']:.3f}{improvement}")
        
        # Real-time performance
        if "realtime_performance" in self.results:
            rt_data = self.results["realtime_performance"]
            if "error" not in rt_data:
                print(f"\n⏱️ Real-time Performance: {rt_data['realtime_performance']:.2%}")
                print(f"   Average latency: {rt_data['avg_latency']:.3f}s")
        
        # Memory usage
        if "memory_analysis" in self.results:
            mem_data = self.results["memory_analysis"]
            if "error" not in mem_data:
                print(f"\n💾 Memory Usage:")
                print(f"   Overhead: {mem_data['memory_overhead_mb']:.1f}MB")
                print(f"   Growth: {mem_data['memory_growth_mb']:.1f}MB")
        
        print("="*60)


async def main():
    """Run STT performance benchmark."""
    benchmark = STTBenchmark()
    
    try:
        results = await benchmark.run_comprehensive_benchmark()
        
        # Print summary
        benchmark.print_summary()
        
        # Save results
        benchmark.save_results()
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Benchmark failed: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
