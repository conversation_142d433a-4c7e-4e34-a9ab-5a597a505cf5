#!/usr/bin/env python3
"""
COMPREHENSIVE PERFORMANCE BASELINE TEST
Establish performance baselines for all voice AI components
"""

import asyncio
import time
import logging
import json
import numpy as np
from typing import Dict, Any, List, Optional
from pathlib import Path
import statistics

# Apply optimizations
import project  # noqa: F401  # side-effects: patches applied

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("perf_baseline")

class PerformanceBaseline:
    """Comprehensive performance baseline testing."""
    
    def __init__(self):
        self.results = {
            "timestamp": time.time(),
            "system_info": {},
            "stt_performance": {},
            "tts_performance": {},
            "llm_performance": {},
            "audio_performance": {},
            "memory_performance": {},
            "end_to_end_performance": {}
        }
        
    async def run_comprehensive_baseline(self) -> Dict[str, Any]:
        """Run complete performance baseline testing."""
        logger.info("🎯 COMPREHENSIVE PERFORMANCE BASELINE STARTING")
        logger.info("=" * 60)
        
        # System Information
        logger.info("💻 Collecting system information...")
        self.results["system_info"] = self._get_system_info()
        
        # STT Performance
        logger.info("🎤 Testing STT performance...")
        self.results["stt_performance"] = await self._test_stt_performance()
        
        # TTS Performance
        logger.info("🔊 Testing TTS performance...")
        self.results["tts_performance"] = await self._test_tts_performance()
        
        # LLM Performance
        logger.info("🧠 Testing LLM performance...")
        self.results["llm_performance"] = await self._test_llm_performance()
        
        # Audio Performance
        logger.info("🎵 Testing audio performance...")
        self.results["audio_performance"] = await self._test_audio_performance()
        
        # Memory Performance
        logger.info("💾 Testing memory performance...")
        self.results["memory_performance"] = await self._test_memory_performance()
        
        # End-to-End Performance
        logger.info("🔄 Testing end-to-end performance...")
        self.results["end_to_end_performance"] = await self._test_end_to_end_performance()
        
        # Generate summary
        self._generate_summary()
        
        logger.info("=" * 60)
        logger.info("✅ COMPREHENSIVE PERFORMANCE BASELINE COMPLETE")
        
        return self.results
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        info = {}
        
        try:
            import psutil
            import platform
            
            # System info
            info["platform"] = platform.platform()
            info["processor"] = platform.processor()
            info["python_version"] = platform.python_version()
            
            # Memory info
            memory = psutil.virtual_memory()
            info["total_memory_gb"] = memory.total / (1024**3)
            info["available_memory_gb"] = memory.available / (1024**3)
            info["memory_percent"] = memory.percent
            
            # CPU info
            info["cpu_count"] = psutil.cpu_count()
            info["cpu_count_logical"] = psutil.cpu_count(logical=True)
            info["cpu_freq"] = psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            
            # Disk info
            disk = psutil.disk_usage('.')
            info["disk_total_gb"] = disk.total / (1024**3)
            info["disk_used_gb"] = disk.used / (1024**3)
            info["disk_free_gb"] = disk.free / (1024**3)
            info["disk_percent"] = (disk.used / disk.total) * 100
            
        except ImportError:
            info["error"] = "psutil not available"
        
        # GPU info
        try:
            import torch
            info["cuda_available"] = torch.cuda.is_available()
            if torch.cuda.is_available():
                info["cuda_device_count"] = torch.cuda.device_count()
                info["cuda_device_name"] = torch.cuda.get_device_name(0)
        except ImportError:
            info["cuda_available"] = False
        
        return info
    
    async def _test_stt_performance(self) -> Dict[str, Any]:
        """Test STT performance."""
        results = {
            "tests_run": 0,
            "total_time": 0,
            "average_rtf": 0,
            "min_rtf": float('inf'),
            "max_rtf": 0,
            "rtf_values": [],
            "errors": []
        }
        
        try:
            from faster_whisper import WhisperModel
            
            # Initialize model
            model = WhisperModel("tiny.en", device="cpu", compute_type="int8")
            
            # Test with different audio lengths
            test_durations = [1, 2, 5, 10]  # seconds
            
            for duration in test_durations:
                try:
                    # Generate test audio
                    sample_rate = 16000
                    audio_length = duration * sample_rate
                    test_audio = np.random.normal(0, 0.1, audio_length).astype(np.float32)
                    
                    # Measure transcription time
                    start_time = time.time()
                    segments, _ = model.transcribe(test_audio, language="en", beam_size=1)
                    list(segments)  # Consume generator
                    end_time = time.time()
                    
                    # Calculate RTF (Real Time Factor)
                    processing_time = end_time - start_time
                    rtf = processing_time / duration
                    
                    results["tests_run"] += 1
                    results["total_time"] += processing_time
                    results["rtf_values"].append(rtf)
                    results["min_rtf"] = min(results["min_rtf"], rtf)
                    results["max_rtf"] = max(results["max_rtf"], rtf)
                    
                    logger.info(f"  STT {duration}s audio: {processing_time:.3f}s (RTF: {rtf:.3f})")
                    
                except Exception as e:
                    results["errors"].append(f"Duration {duration}s: {str(e)}")
                    logger.error(f"  STT test failed for {duration}s: {e}")
            
            if results["rtf_values"]:
                results["average_rtf"] = statistics.mean(results["rtf_values"])
                results["median_rtf"] = statistics.median(results["rtf_values"])
                results["rtf_std"] = statistics.stdev(results["rtf_values"]) if len(results["rtf_values"]) > 1 else 0
            
        except Exception as e:
            results["errors"].append(f"STT initialization failed: {str(e)}")
            logger.error(f"  STT testing failed: {e}")
        
        return results
    
    async def _test_tts_performance(self) -> Dict[str, Any]:
        """Test TTS performance."""
        results = {
            "tests_run": 0,
            "total_time": 0,
            "average_latency": 0,
            "first_frame_latencies": [],
            "total_latencies": [],
            "errors": []
        }
        
        try:
            import edge_tts
            
            # Test phrases of different lengths
            test_phrases = [
                "Hello",
                "Hello, how are you today?",
                "This is a longer sentence to test the text-to-speech performance with more content.",
                "This is an even longer paragraph that contains multiple sentences. It should help us understand how the TTS system performs with longer content. We want to measure both the time to first audio frame and the total processing time."
            ]
            
            for i, phrase in enumerate(test_phrases):
                try:
                    start_time = time.time()
                    
                    # Create TTS
                    communicate = edge_tts.Communicate(phrase, "en-US-AriaNeural")
                    
                    # Measure time to first chunk
                    first_frame_time = None
                    chunks = []
                    
                    async for chunk in communicate.stream():
                        if chunk["type"] == "audio" and first_frame_time is None:
                            first_frame_time = time.time()
                        if chunk["type"] == "audio":
                            chunks.append(chunk["data"])
                    
                    end_time = time.time()
                    
                    # Calculate metrics
                    total_latency = end_time - start_time
                    first_frame_latency = (first_frame_time - start_time) * 1000 if first_frame_time else 0  # ms
                    
                    results["tests_run"] += 1
                    results["total_time"] += total_latency
                    results["first_frame_latencies"].append(first_frame_latency)
                    results["total_latencies"].append(total_latency)
                    
                    logger.info(f"  TTS phrase {i+1}: {total_latency:.3f}s total, {first_frame_latency:.1f}ms first frame")
                    
                except Exception as e:
                    results["errors"].append(f"Phrase {i+1}: {str(e)}")
                    logger.error(f"  TTS test failed for phrase {i+1}: {e}")
            
            if results["first_frame_latencies"]:
                results["average_first_frame"] = statistics.mean(results["first_frame_latencies"])
                results["median_first_frame"] = statistics.median(results["first_frame_latencies"])
            
            if results["total_latencies"]:
                results["average_latency"] = statistics.mean(results["total_latencies"])
                results["median_latency"] = statistics.median(results["total_latencies"])
            
        except Exception as e:
            results["errors"].append(f"TTS initialization failed: {str(e)}")
            logger.error(f"  TTS testing failed: {e}")
        
        return results
    
    async def _test_llm_performance(self) -> Dict[str, Any]:
        """Test LLM performance."""
        results = {
            "tests_run": 0,
            "total_time": 0,
            "average_response_time": 0,
            "response_times": [],
            "errors": []
        }
        
        try:
            import ollama
            
            # Test queries of different complexity
            test_queries = [
                "Hello",
                "What is the weather like?",
                "Explain the concept of machine learning in simple terms.",
                "Write a short story about a robot learning to understand human emotions."
            ]
            
            for i, query in enumerate(test_queries):
                try:
                    start_time = time.time()
                    
                    response = ollama.chat(
                        model='llama3.2:1b',
                        messages=[{'role': 'user', 'content': query}]
                    )
                    
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    results["tests_run"] += 1
                    results["total_time"] += response_time
                    results["response_times"].append(response_time)
                    
                    logger.info(f"  LLM query {i+1}: {response_time:.3f}s")
                    
                except Exception as e:
                    results["errors"].append(f"Query {i+1}: {str(e)}")
                    logger.error(f"  LLM test failed for query {i+1}: {e}")
            
            if results["response_times"]:
                results["average_response_time"] = statistics.mean(results["response_times"])
                results["median_response_time"] = statistics.median(results["response_times"])
            
        except Exception as e:
            results["errors"].append(f"LLM testing failed: {str(e)}")
            logger.error(f"  LLM testing failed: {e}")
        
        return results
    
    async def _test_audio_performance(self) -> Dict[str, Any]:
        """Test audio performance."""
        results = {
            "backends_tested": [],
            "backend_results": {},
            "errors": []
        }
        
        # Test pygame
        try:
            import pygame
            pygame.mixer.init(frequency=22050, size=-16, channels=1, buffer=512)
            
            start_time = time.time()
            # Test audio generation
            duration = 1.0
            sample_rate = 22050
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            wave = np.sin(440 * 2 * np.pi * t) * 0.3
            sound_array = (wave * 32767).astype(np.int16)
            
            # Convert to pygame sound
            sound = pygame.sndarray.make_sound(sound_array)
            end_time = time.time()
            
            pygame.mixer.quit()
            
            results["backends_tested"].append("pygame")
            results["backend_results"]["pygame"] = {
                "initialization_time": end_time - start_time,
                "status": "success"
            }
            
        except Exception as e:
            results["errors"].append(f"pygame: {str(e)}")
        
        # Test sounddevice
        try:
            import sounddevice as sd
            
            start_time = time.time()
            devices = sd.query_devices()
            end_time = time.time()
            
            results["backends_tested"].append("sounddevice")
            results["backend_results"]["sounddevice"] = {
                "query_time": end_time - start_time,
                "device_count": len(devices),
                "status": "success"
            }
            
        except Exception as e:
            results["errors"].append(f"sounddevice: {str(e)}")
        
        return results
    
    async def _test_memory_performance(self) -> Dict[str, Any]:
        """Test memory performance."""
        results = {
            "initial_memory": 0,
            "peak_memory": 0,
            "final_memory": 0,
            "memory_growth": 0,
            "gc_collections": 0
        }
        
        try:
            import psutil
            import gc
            
            # Initial memory
            process = psutil.Process()
            results["initial_memory"] = process.memory_info().rss / 1024 / 1024  # MB
            
            # Stress test memory
            test_data = []
            for i in range(100):
                # Create some data
                data = np.random.random((1000, 1000))
                test_data.append(data)
                
                # Check memory periodically
                if i % 10 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    results["peak_memory"] = max(results["peak_memory"], current_memory)
            
            # Force garbage collection
            gc.collect()
            results["gc_collections"] = 1
            
            # Final memory
            results["final_memory"] = process.memory_info().rss / 1024 / 1024
            results["memory_growth"] = results["final_memory"] - results["initial_memory"]
            
        except Exception as e:
            results["error"] = str(e)
        
        return results
    
    async def _test_end_to_end_performance(self) -> Dict[str, Any]:
        """Test end-to-end performance."""
        results = {
            "tests_run": 0,
            "total_times": [],
            "average_total_time": 0,
            "errors": []
        }
        
        # Simulate end-to-end voice interaction
        test_scenarios = [
            "Quick response test",
            "Medium length conversation test",
            "Complex query requiring detailed response"
        ]
        
        for i, scenario in enumerate(test_scenarios):
            try:
                start_time = time.time()
                
                # Simulate the full pipeline:
                # 1. Audio processing (simulated)
                await asyncio.sleep(0.1)  # STT processing time
                
                # 2. LLM processing (simulated)
                await asyncio.sleep(0.5)  # LLM response time
                
                # 3. TTS processing (simulated)
                await asyncio.sleep(0.2)  # TTS generation time
                
                # 4. Audio output (simulated)
                await asyncio.sleep(0.1)  # Audio playback setup
                
                end_time = time.time()
                total_time = end_time - start_time
                
                results["tests_run"] += 1
                results["total_times"].append(total_time)
                
                logger.info(f"  E2E scenario {i+1}: {total_time:.3f}s")
                
            except Exception as e:
                results["errors"].append(f"Scenario {i+1}: {str(e)}")
        
        if results["total_times"]:
            results["average_total_time"] = statistics.mean(results["total_times"])
            results["median_total_time"] = statistics.median(results["total_times"])
        
        return results
    
    def _generate_summary(self) -> None:
        """Generate performance summary."""
        summary = {
            "overall_status": "completed",
            "key_metrics": {},
            "recommendations": []
        }
        
        # Extract key metrics
        if "average_rtf" in self.results["stt_performance"]:
            summary["key_metrics"]["stt_rtf"] = self.results["stt_performance"]["average_rtf"]
            
        if "average_first_frame" in self.results["tts_performance"]:
            summary["key_metrics"]["tts_first_frame_ms"] = self.results["tts_performance"]["average_first_frame"]
            
        if "average_response_time" in self.results["llm_performance"]:
            summary["key_metrics"]["llm_response_time"] = self.results["llm_performance"]["average_response_time"]
            
        if "average_total_time" in self.results["end_to_end_performance"]:
            summary["key_metrics"]["e2e_total_time"] = self.results["end_to_end_performance"]["average_total_time"]
        
        # Generate recommendations
        if summary["key_metrics"].get("stt_rtf", 0) > 0.35:
            summary["recommendations"].append("STT RTF is high - consider GPU acceleration or model optimization")
            
        if summary["key_metrics"].get("tts_first_frame_ms", 0) > 150:
            summary["recommendations"].append("TTS first frame latency is high - optimize streaming")
            
        if summary["key_metrics"].get("e2e_total_time", 0) > 2.0:
            summary["recommendations"].append("End-to-end latency is high - optimize pipeline parallelization")
        
        self.results["summary"] = summary
    
    def save_results(self, filename: str = "performance_baseline.json") -> None:
        """Save results to file."""
        try:
            with open(filename, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            logger.info(f"✅ Performance baseline saved to {filename}")
        except Exception as e:
            logger.error(f"❌ Failed to save baseline: {e}")


async def main():
    """Run performance baseline testing."""
    baseline = PerformanceBaseline()
    
    try:
        results = await baseline.run_comprehensive_baseline()
        
        # Print summary
        print("\n" + "="*60)
        print("🎯 PERFORMANCE BASELINE SUMMARY")
        print("="*60)
        
        summary = results.get("summary", {})
        key_metrics = summary.get("key_metrics", {})
        
        if "stt_rtf" in key_metrics:
            print(f"🎤 STT RTF: {key_metrics['stt_rtf']:.3f}")
        if "tts_first_frame_ms" in key_metrics:
            print(f"🔊 TTS First Frame: {key_metrics['tts_first_frame_ms']:.1f}ms")
        if "llm_response_time" in key_metrics:
            print(f"🧠 LLM Response: {key_metrics['llm_response_time']:.3f}s")
        if "e2e_total_time" in key_metrics:
            print(f"🔄 End-to-End: {key_metrics['e2e_total_time']:.3f}s")
        
        recommendations = summary.get("recommendations", [])
        if recommendations:
            print("\n📋 RECOMMENDATIONS:")
            for rec in recommendations:
                print(f"  • {rec}")
        
        print("="*60)
        
        # Save results
        baseline.save_results()
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Baseline testing failed: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
