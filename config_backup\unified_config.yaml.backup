# Fixed Voice AI System Configuration
system:
  name: "Fixed Voice AI System"
  version: "1.0.0"
  platform: "windows"
  python_version: "3.12"

audio:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  format: "wav"
  backend: "sounddevice"

stt:
  primary_provider: "faster_whisper"
  fallback_provider: "system"
  model: "tiny.en"
  language: "en"
  device: "cpu"

tts:
  primary_provider: "edge_tts"
  fallback_provider: "pyttsx3"
  voice: "en-US-AriaNeural"
  rate: 200
  volume: 0.9

llm:
  provider: "ollama"
  base_url: "http://localhost:11434"
  model: "deepseek-r1:14b"
  max_tokens: 150
  temperature: 0.7

performance:
  target_latency_ms: 500
  streaming_enabled: true
  interruption_enabled: true
