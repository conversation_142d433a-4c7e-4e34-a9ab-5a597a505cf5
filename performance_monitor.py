#!/usr/bin/env python3
"""performance_monitor.py

Comprehensive performance monitoring for Voice AI system.
Tracks metrics, identifies bottlenecks, and provides optimization recommendations.
"""

import asyncio
import logging
import time
import psutil
import sys
from pathlib import Path
from typing import Dict, Any, List
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("performance_monitor")

class PerformanceMonitor:
    """Comprehensive performance monitoring for Voice AI."""
    
    def __init__(self):
        self.metrics = {
            "system": {},
            "voice_ai": {},
            "ollama": {},
            "audio": {},
            "recommendations": []
        }
        self.start_time = time.time()
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get current system performance metrics."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                "cpu_usage_percent": cpu_percent,
                "memory_total_gb": round(memory.total / (1024**3), 2),
                "memory_used_gb": round(memory.used / (1024**3), 2),
                "memory_available_gb": round(memory.available / (1024**3), 2),
                "memory_usage_percent": memory.percent,
                "disk_total_gb": round(disk.total / (1024**3), 2),
                "disk_used_gb": round(disk.used / (1024**3), 2),
                "disk_free_gb": round(disk.free / (1024**3), 2),
                "disk_usage_percent": round((disk.used / disk.total) * 100, 2),
                "process_count": len(psutil.pids()),
                "boot_time": psutil.boot_time(),
                "uptime_hours": round((time.time() - psutil.boot_time()) / 3600, 2)
            }
        except Exception as e:
            logger.error(f"❌ Failed to get system metrics: {e}")
            return {}
    
    def check_ollama_status(self) -> Dict[str, Any]:
        """Check Ollama service status and performance."""
        try:
            import ollama
            
            # Check if Ollama is running
            models = ollama.list()
            available_models = [model.model for model in models.models]
            
            # Test response time
            start_time = time.time()
            test_response = ollama.generate(
                model="llama3.2:latest",
                prompt="Hello",
                stream=False
            )
            response_time = time.time() - start_time
            
            return {
                "status": "healthy",
                "available_models": available_models,
                "model_count": len(available_models),
                "test_response_time": round(response_time, 3),
                "test_response_length": len(test_response.get("response", "")),
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "available_models": [],
                "model_count": 0
            }
    
    def check_audio_backends(self) -> Dict[str, Any]:
        """Check available audio backends and their status."""
        backends = {}
        
        # Check pygame
        try:
            import pygame
            pygame.mixer.init()
            backends["pygame"] = {
                "available": True,
                "version": pygame.version.ver,
                "status": "initialized"
            }
            pygame.mixer.quit()
        except Exception as e:
            backends["pygame"] = {
                "available": False,
                "error": str(e)
            }
        
        # Check simpleaudio
        try:
            import simpleaudio
            backends["simpleaudio"] = {
                "available": True,
                "status": "available"
            }
        except Exception as e:
            backends["simpleaudio"] = {
                "available": False,
                "error": str(e)
            }
        
        # Check sounddevice
        try:
            import sounddevice as sd
            backends["sounddevice"] = {
                "available": True,
                "default_device": sd.default.device,
                "input_devices": len(sd.query_devices(kind='input')),
                "output_devices": len(sd.query_devices(kind='output'))
            }
        except Exception as e:
            backends["sounddevice"] = {
                "available": False,
                "error": str(e)
            }
        
        return backends
    
    def check_dependencies(self) -> Dict[str, Any]:
        """Check critical dependencies and their versions."""
        dependencies = {}
        
        critical_deps = [
            "faster_whisper", "edge_tts", "ollama", "numpy", 
            "webrtcvad", "pygame", "sounddevice", "tomli"
        ]
        
        for dep in critical_deps:
            try:
                module = __import__(dep)
                version = getattr(module, "__version__", "unknown")
                dependencies[dep] = {
                    "installed": True,
                    "version": version,
                    "status": "ok"
                }
            except ImportError as e:
                dependencies[dep] = {
                    "installed": False,
                    "error": str(e),
                    "status": "missing"
                }
            except Exception as e:
                dependencies[dep] = {
                    "installed": True,
                    "error": str(e),
                    "status": "error"
                }
        
        return dependencies
    
    def analyze_performance(self) -> List[str]:
        """Analyze metrics and provide optimization recommendations."""
        recommendations = []
        system = self.metrics.get("system", {})
        
        # CPU Analysis
        cpu_usage = system.get("cpu_usage_percent", 0)
        if cpu_usage > 80:
            recommendations.append("🔥 HIGH CPU USAGE: Consider using a smaller Ollama model or reducing audio quality")
        elif cpu_usage > 60:
            recommendations.append("⚠️ MODERATE CPU USAGE: Monitor performance during voice interactions")
        
        # Memory Analysis
        memory_usage = system.get("memory_usage_percent", 0)
        memory_available = system.get("memory_available_gb", 0)
        
        if memory_usage > 85:
            recommendations.append("🔥 HIGH MEMORY USAGE: Consider restarting the system or closing other applications")
        elif memory_available < 2:
            recommendations.append("⚠️ LOW AVAILABLE MEMORY: May cause audio stuttering or model loading issues")
        
        # Disk Analysis
        disk_usage = system.get("disk_usage_percent", 0)
        disk_free = system.get("disk_free_gb", 0)
        
        if disk_usage > 90:
            recommendations.append("🔥 DISK ALMOST FULL: Clean up temporary files and logs")
        elif disk_free < 5:
            recommendations.append("⚠️ LOW DISK SPACE: May cause temporary file creation issues")
        
        # Ollama Analysis
        ollama = self.metrics.get("ollama", {})
        if ollama.get("status") != "healthy":
            recommendations.append("❌ OLLAMA ISSUES: Check Ollama service status and model availability")
        else:
            response_time = ollama.get("test_response_time", 0)
            if response_time > 3:
                recommendations.append("🐌 SLOW OLLAMA RESPONSE: Consider using a faster model or checking system load")
            elif response_time > 1.5:
                recommendations.append("⚠️ MODERATE OLLAMA LATENCY: Monitor response times during voice interactions")
        
        # Audio Backend Analysis
        audio = self.metrics.get("audio", {})
        available_backends = sum(1 for backend in audio.values() if backend.get("available", False))
        
        if available_backends == 0:
            recommendations.append("❌ NO AUDIO BACKENDS: Install pygame, simpleaudio, or fix sounddevice")
        elif available_backends == 1:
            recommendations.append("⚠️ LIMITED AUDIO BACKENDS: Install additional backends for better fallback support")
        
        # Performance Optimizations
        if not recommendations:
            recommendations.append("✅ SYSTEM PERFORMANCE: All metrics within optimal ranges")
            recommendations.append("💡 OPTIMIZATION TIP: Use 'tiny.en' Whisper model for fastest STT")
            recommendations.append("💡 OPTIMIZATION TIP: Keep Ollama responses under 150 tokens for speed")
        
        return recommendations
    
    async def run_comprehensive_check(self) -> Dict[str, Any]:
        """Run a comprehensive performance check."""
        logger.info("🔍 Starting comprehensive performance analysis...")
        
        # Collect metrics
        self.metrics["system"] = self.get_system_metrics()
        self.metrics["ollama"] = await asyncio.to_thread(self.check_ollama_status)
        self.metrics["audio"] = self.check_audio_backends()
        self.metrics["dependencies"] = self.check_dependencies()
        
        # Generate recommendations
        self.metrics["recommendations"] = self.analyze_performance()
        
        # Add metadata
        self.metrics["timestamp"] = time.time()
        self.metrics["analysis_duration"] = round(time.time() - self.start_time, 2)
        
        return self.metrics
    
    def print_report(self):
        """Print a formatted performance report."""
        print("\n" + "="*80)
        print("🎯 VOICE AI PERFORMANCE ANALYSIS REPORT")
        print("="*80)
        
        # System Overview
        system = self.metrics.get("system", {})
        print(f"\n📊 SYSTEM OVERVIEW:")
        print(f"   CPU Usage: {system.get('cpu_usage_percent', 0):.1f}%")
        print(f"   Memory: {system.get('memory_used_gb', 0):.1f}GB / {system.get('memory_total_gb', 0):.1f}GB ({system.get('memory_usage_percent', 0):.1f}%)")
        print(f"   Disk: {system.get('disk_used_gb', 0):.1f}GB / {system.get('disk_total_gb', 0):.1f}GB ({system.get('disk_usage_percent', 0):.1f}%)")
        print(f"   Uptime: {system.get('uptime_hours', 0):.1f} hours")
        
        # Ollama Status
        ollama = self.metrics.get("ollama", {})
        print(f"\n🤖 OLLAMA STATUS:")
        print(f"   Status: {ollama.get('status', 'unknown')}")
        print(f"   Available Models: {ollama.get('model_count', 0)}")
        print(f"   Test Response Time: {ollama.get('test_response_time', 0):.3f}s")
        
        # Audio Backends
        audio = self.metrics.get("audio", {})
        print(f"\n🔊 AUDIO BACKENDS:")
        for backend, info in audio.items():
            status = "✅" if info.get("available", False) else "❌"
            print(f"   {status} {backend}: {info.get('status', info.get('error', 'unknown'))}")
        
        # Dependencies
        deps = self.metrics.get("dependencies", {})
        print(f"\n📦 DEPENDENCIES:")
        for dep, info in deps.items():
            status = "✅" if info.get("installed", False) else "❌"
            version = info.get("version", "unknown")
            print(f"   {status} {dep}: {version}")
        
        # Recommendations
        recommendations = self.metrics.get("recommendations", [])
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in recommendations:
            print(f"   {rec}")
        
        print(f"\n⏱️  Analysis completed in {self.metrics.get('analysis_duration', 0):.2f}s")
        print("="*80 + "\n")
    
    def save_report(self, filename: str = "performance_report.json"):
        """Save the performance report to a JSON file."""
        try:
            with open(filename, 'w') as f:
                json.dump(self.metrics, f, indent=2, default=str)
            logger.info(f"✅ Performance report saved to {filename}")
        except Exception as e:
            logger.error(f"❌ Failed to save report: {e}")


async def main():
    """Run the performance monitor."""
    monitor = PerformanceMonitor()
    
    try:
        await monitor.run_comprehensive_check()
        monitor.print_report()
        monitor.save_report()
        
        return 0
    except Exception as e:
        logger.error(f"❌ Performance monitoring failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main())) 