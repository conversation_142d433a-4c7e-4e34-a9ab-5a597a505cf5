[audio]
sample_rate = 16000         # Hz - Optimized for voice
chunk_duration = 0.02       # seconds - Reduced for lower latency  
vad_level = 2               # 0–3, higher = stricter
max_silence = 0.8           # seconds - Faster response
min_speech = 0.2            # seconds - Minimum speech detection

[stt]
model_size = "tiny.en"      # Fastest model for real-time
beam_size = 1               # Fastest beam search
language = "en"             # Fixed language for speed

[tts]
voice = "en-US-AriaNeural"  # Fast, natural voice
rate = "+0%"                # Normal speaking rate
volume = "+0%"              # Normal volume

[llm]
fast_model = "llama3.2:latest"      # Primary model for speed
reasoning_model = "llama3.2:latest" # Use same model for consistency  
research_model = "llama3.2:latest"  # Use same model for consistency
max_tokens = 150                    # Limit response length for speed
temperature = 0.7                   # Balanced creativity/consistency

[performance]
audio_buffer_size = 512     # Optimized buffer size
max_queue_size = 50         # Prevent memory bloat
cleanup_retries = 3         # File cleanup attempts
response_timeout = 10.0     # seconds - Prevent hanging 