{"system": {"name": "Fixed Voice AI System", "version": "1.0.0", "platform": "windows", "python_version": "3.12"}, "audio": {"sample_rate": 16000, "channels": 1, "chunk_size": 1024, "format": "wav", "backend": "sounddevice"}, "stt": {"primary_provider": "faster_whisper", "fallback_provider": "system", "model": "tiny.en", "language": "en", "device": "cpu"}, "tts": {"primary_provider": "edge_tts", "fallback_provider": "pyttsx3", "system_fallback": "windows_sapi", "voice": "en-US-AriaNeural", "rate": 200, "volume": 0.9}, "llm": {"provider": "ollama", "base_url": "http://localhost:11434", "model": "deepseek-r1:14b", "fallback_model": "llama3.2:latest", "max_tokens": 150, "temperature": 0.7}, "performance": {"target_latency_ms": 500, "target_memory_mb": 2048, "target_cpu_percent": 30, "gpu_acceleration": false, "streaming_enabled": true, "interruption_enabled": true}, "features": {"voice_activity_detection": true, "noise_suppression": false, "echo_cancellation": false, "automatic_gain_control": false}}