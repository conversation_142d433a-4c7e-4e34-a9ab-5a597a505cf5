audio:
  backend: !!python/object/apply:__main__.AudioBackend
  - auto
  bit_depth: 16
  buffer_size: 512
  channels: 1
  format: PCM
  sample_rate: 16000
  vad_aggressiveness: 2
  vad_mode: webrtc
debug: false
environment: development
llm:
  base_url: http://localhost:11434
  max_tokens: 2048
  model: llama3.2:1b
  provider: !!python/object/apply:__main__.LLMProvider
  - ollama
  system_prompt: You are a helpful AI assistant.
  temperature: 0.7
  timeout: 30.0
  top_k: 40
  top_p: 0.9
monitoring:
  alert_disk_threshold: 90.0
  alert_memory_threshold: 85.0
  alert_response_time_threshold: 5.0
  collect_cpu: true
  collect_disk: true
  collect_memory: true
  collect_performance: true
  enabled: true
  interval_seconds: 30
  log_file: voice_ai.log
  log_level: INFO
  log_rotation: true
  max_log_size_mb: 100
performance:
  aggressive_cleanup_threshold: 24
  audio_buffer_size: 512
  audio_sample_rate: 16000
  e2e_total_target: 2.0
  llm_max_tokens: 2048
  llm_response_timeout: 30.0
  max_audio_buffers: 50
  max_chat_history: 32
  memory_critical_threshold: 85.0
  memory_warning_threshold: 75.0
  stt_cpu_threads: 4
  stt_model_cache_size: 3
  stt_rtf_target: 0.25
  tts_cache_enabled: true
  tts_first_frame_target_ms: 100.0
  tts_streaming_enabled: true
stt:
  beam_size: 1
  best_of: 1
  compute_type: int8
  device: auto
  language: en
  model: tiny.en
  provider: !!python/object/apply:__main__.STTProvider
  - faster_whisper
  temperature: 0.0
  vad_filter: true
  vad_parameters:
    max_speech_duration_s: 30
    min_silence_duration_ms: 2000
    min_speech_duration_ms: 250
    speech_pad_ms: 400
    threshold: 0.5
    window_size_samples: 1024
tts:
  chunk_size: 1024
  format: audio-24khz-48kbitrate-mono-mp3
  pitch: +0Hz
  provider: !!python/object/apply:__main__.TTSProvider
  - edge_tts
  quality: high
  rate: +0%
  streaming: true
  voice: en-US-AriaNeural
