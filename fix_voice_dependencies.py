#!/usr/bin/env python3
"""
🔧 VOICE DEPENDENCIES FIXER
Fixes all dependency issues and installs missing packages for voice system
"""

import subprocess
import sys
import os
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VoiceDependencyFixer:
    """Fixes all voice system dependency issues"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.venv_path = self.base_dir / ".venv"
        self.python_exe = self.venv_path / "Scripts" / "python.exe"
        self.pip_exe = self.venv_path / "Scripts" / "pip.exe"
        
    def log(self, message: str, level: str = "INFO"):
        """Log message with emoji"""
        emoji_map = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "WARNING": "⚠️",
            "ERROR": "❌"
        }
        print(f"{emoji_map.get(level, 'ℹ️')} {message}")
        getattr(logger, level.lower())(message)
    
    def run_command(self, cmd: list, timeout: int = 300) -> tuple[bool, str]:
        """Run command and return success status and output"""
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=timeout,
                cwd=self.base_dir
            )
            return result.returncode == 0, result.stdout + result.stderr
        except subprocess.TimeoutExpired:
            return False, f"Command timed out after {timeout}s"
        except Exception as e:
            return False, str(e)
    
    def fix_pip_installation(self) -> bool:
        """Fix broken pip installation"""
        self.log("🔧 Fixing pip installation...")
        
        # Method 1: Try to reinstall pip using ensurepip
        self.log("  Trying ensurepip method...")
        success, output = self.run_command([str(self.python_exe), "-m", "ensurepip", "--upgrade"])
        if success:
            self.log("  ✅ pip fixed with ensurepip", "SUCCESS")
            return True
        
        # Method 2: Download and install pip manually
        self.log("  Trying manual pip installation...")
        try:
            import urllib.request
            get_pip_url = "https://bootstrap.pypa.io/get-pip.py"
            get_pip_path = self.base_dir / "get-pip.py"
            
            self.log(f"  Downloading {get_pip_url}...")
            urllib.request.urlretrieve(get_pip_url, get_pip_path)
            
            success, output = self.run_command([str(self.python_exe), str(get_pip_path)])
            if success:
                self.log("  ✅ pip installed manually", "SUCCESS")
                get_pip_path.unlink()  # Clean up
                return True
            else:
                self.log(f"  ❌ Manual pip installation failed: {output}", "ERROR")
                
        except Exception as e:
            self.log(f"  ❌ Failed to download pip: {e}", "ERROR")
        
        # Method 3: Try using python -m pip directly
        self.log("  Trying direct pip module...")
        success, output = self.run_command([str(self.python_exe), "-m", "pip", "--version"])
        if success:
            self.log("  ✅ pip is actually working", "SUCCESS")
            return True
        
        return False
    
    def install_package(self, package: str, version: str = None) -> bool:
        """Install a single package"""
        package_spec = f"{package}=={version}" if version else package
        
        # Try multiple installation methods
        methods = [
            [str(self.python_exe), "-m", "pip", "install", package_spec],
            [str(self.pip_exe), "install", package_spec],
            [str(self.python_exe), "-m", "pip", "install", "--user", package_spec]
        ]
        
        for method in methods:
            self.log(f"  Installing {package_spec}...")
            success, output = self.run_command(method)
            if success:
                self.log(f"  ✅ {package} installed successfully", "SUCCESS")
                return True
            else:
                self.log(f"  ⚠️ Method failed: {output[:100]}...", "WARNING")
        
        return False
    
    def verify_package(self, package: str) -> bool:
        """Verify package is installed and working"""
        try:
            success, output = self.run_command([str(self.python_exe), "-c", f"import {package}; print(f'{package}: OK')"])
            return success
        except:
            return False
    
    def install_voice_dependencies(self) -> bool:
        """Install all required voice dependencies"""
        self.log("📦 Installing voice dependencies...")
        
        # Core dependencies with specific versions
        dependencies = [
            ("pip", "24.0"),
            ("setuptools", "69.0.0"),
            ("wheel", "0.42.0"),
            ("numpy", "1.26.4"),
            ("scipy", "1.11.4"),
            ("PyYAML", "6.0.1"),
            ("faster-whisper", "1.0.3"),
            ("edge-tts", "7.0.2"),
            ("webrtcvad", "2.0.10"),
            ("sounddevice", "0.4.7"),
            ("pygame", "2.6.1"),
            ("simpleaudio", "1.0.4"),
            ("soundfile", "0.12.1"),
            ("pyttsx3", "2.90"),
            ("ollama", "0.4.0"),
            ("psutil", "5.9.8"),
            ("tomli", "2.0.1"),
            ("pytest", "8.0.0"),
            ("pytest-asyncio", "0.23.0")
        ]
        
        installed_count = 0
        failed_packages = []
        
        for package, version in dependencies:
            if self.install_package(package, version):
                installed_count += 1
            else:
                failed_packages.append(package)
                # Try without version constraint
                if self.install_package(package):
                    installed_count += 1
                    failed_packages.remove(package)
        
        self.log(f"📊 Installation summary: {installed_count}/{len(dependencies)} packages installed")
        
        if failed_packages:
            self.log(f"⚠️ Failed packages: {', '.join(failed_packages)}", "WARNING")
        
        return len(failed_packages) == 0
    
    def install_pytorch(self) -> bool:
        """Install PyTorch for GPU acceleration"""
        self.log("🔥 Installing PyTorch...")
        
        # Try CPU version first (more reliable)
        cpu_success = self.install_package("torch", "2.1.0+cpu")
        if cpu_success:
            self.log("✅ PyTorch CPU installed", "SUCCESS")
            return True
        
        # Try regular version
        regular_success = self.install_package("torch")
        if regular_success:
            self.log("✅ PyTorch installed", "SUCCESS")
            return True
        
        self.log("❌ PyTorch installation failed", "ERROR")
        return False
    
    def verify_installation(self) -> bool:
        """Verify all packages are working"""
        self.log("🔍 Verifying installation...")
        
        test_packages = [
            "numpy", "scipy", "yaml", "faster_whisper", 
            "edge_tts", "webrtcvad", "sounddevice", 
            "pygame", "simpleaudio", "soundfile", 
            "pyttsx3", "ollama", "psutil"
        ]
        
        working_count = 0
        for package in test_packages:
            if self.verify_package(package):
                self.log(f"  ✅ {package}: Working")
                working_count += 1
            else:
                self.log(f"  ❌ {package}: Failed", "WARNING")
        
        success_rate = working_count / len(test_packages)
        self.log(f"📊 Verification: {working_count}/{len(test_packages)} packages working ({success_rate:.1%})")
        
        return success_rate >= 0.8  # 80% success rate required
    
    def fix_all_dependencies(self) -> bool:
        """Main method to fix all dependency issues"""
        self.log("🚀 FIXING VOICE SYSTEM DEPENDENCIES")
        self.log("=" * 60)
        
        # Step 1: Fix pip
        if not self.fix_pip_installation():
            self.log("❌ Failed to fix pip installation", "ERROR")
            return False
        
        # Step 2: Install voice dependencies
        if not self.install_voice_dependencies():
            self.log("⚠️ Some voice dependencies failed to install", "WARNING")
        
        # Step 3: Install PyTorch
        if not self.install_pytorch():
            self.log("⚠️ PyTorch installation failed", "WARNING")
        
        # Step 4: Verify installation
        if not self.verify_installation():
            self.log("❌ Installation verification failed", "ERROR")
            return False
        
        self.log("=" * 60)
        self.log("✅ VOICE DEPENDENCIES FIXED SUCCESSFULLY!", "SUCCESS")
        return True

def main():
    """Main function"""
    fixer = VoiceDependencyFixer()
    success = fixer.fix_all_dependencies()
    
    if success:
        print("\n🎉 SUCCESS! Voice dependencies are now fixed.")
        print("🎯 Next steps:")
        print("  1. Run: python test_voice_system.py")
        print("  2. Run: python run_local_voice_test.py")
        print("  3. Start voice conversations!")
    else:
        print("\n❌ FAILED! Some issues remain.")
        print("🔧 Try running this script again or check the logs.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
