#!/usr/bin/env python3
"""
🎤 SIMPLE VOICE CHAT - WORKING VOICE CONVERSATIONS
A simple, working voice chat system using existing optimizations
"""

import asyncio
import logging
import time
import tempfile
import os
import sys
from pathlib import Path

# Apply all voice optimizations first
try:
    import project  # This applies all the voice patches and optimizations
    print("✅ Voice optimizations loaded")
except ImportError:
    print("⚠️ Voice optimizations not available")

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("simple-voice-chat")

class SimpleVoiceChat:
    """Simple voice chat system using existing optimizations"""
    
    def __init__(self):
        self.conversation_count = 0
        self.is_running = False
        
        # Initialize components
        self.stt_engine = None
        self.llm_client = None
        
        print("🎤 Simple Voice Chat initialized")
    
    def check_dependencies(self):
        """Check available dependencies"""
        deps = {}
        
        # Check STT
        try:
            import faster_whisper
            deps['stt'] = 'faster_whisper'
            print("✅ STT: faster-whisper available")
        except ImportError:
            deps['stt'] = 'fallback'
            print("⚠️ STT: Using fallback")
        
        # Check TTS
        try:
            import edge_tts
            deps['tts'] = 'edge_tts'
            print("✅ TTS: edge-tts available")
        except ImportError:
            try:
                import pyttsx3
                deps['tts'] = 'pyttsx3'
                print("✅ TTS: pyttsx3 available")
            except ImportError:
                deps['tts'] = 'system'
                print("⚠️ TTS: Using system fallback")
        
        # Check LLM
        try:
            import ollama
            models = ollama.list()
            if models.get('models'):
                deps['llm'] = 'ollama'
                available_models = [m['name'] for m in models['models']]
                print(f"✅ LLM: Ollama available - {len(available_models)} models")
            else:
                deps['llm'] = 'echo'
                print("⚠️ LLM: No models, using echo")
        except Exception:
            deps['llm'] = 'echo'
            print("⚠️ LLM: Using echo fallback")
        
        return deps
    
    def init_stt(self, deps):
        """Initialize STT engine"""
        if deps['stt'] == 'faster_whisper':
            try:
                from faster_whisper import WhisperModel
                self.stt_engine = WhisperModel("tiny.en", device="cpu")
                print("✅ STT engine ready (faster-whisper)")
                return True
            except Exception as e:
                print(f"❌ STT initialization failed: {e}")
        
        # Fallback
        self.stt_engine = "fallback"
        print("✅ STT engine ready (fallback)")
        return True
    
    def init_llm(self, deps):
        """Initialize LLM client"""
        if deps['llm'] == 'ollama':
            try:
                import ollama
                self.llm_client = ollama
                print("✅ LLM client ready (Ollama)")
                return True
            except Exception as e:
                print(f"❌ LLM initialization failed: {e}")
        
        # Fallback
        self.llm_client = "echo"
        print("✅ LLM client ready (echo)")
        return True
    
    def transcribe_audio(self, audio_file):
        """Transcribe audio to text"""
        try:
            if hasattr(self.stt_engine, 'transcribe'):
                segments, info = self.stt_engine.transcribe(audio_file)
                text = " ".join([segment.text for segment in segments])
                return text.strip()
            else:
                # Fallback - simulate transcription
                return "Hello, I'm listening to you"
        except Exception as e:
            print(f"❌ Transcription error: {e}")
            return None
    
    def get_ai_response(self, user_input):
        """Get AI response"""
        try:
            if hasattr(self.llm_client, 'chat'):
                # Use Ollama
                response = self.llm_client.chat(
                    model='deepseek-r1:14b',
                    messages=[{
                        'role': 'user', 
                        'content': f"Respond briefly to: {user_input}"
                    }]
                )
                return response['message']['content']
            else:
                # Echo fallback
                responses = [
                    f"That's interesting that you said: {user_input}",
                    f"I understand you mentioned: {user_input}",
                    f"Thanks for telling me: {user_input}",
                    f"I heard you say: {user_input}. Can you tell me more?"
                ]
                import random
                return random.choice(responses)
        except Exception as e:
            print(f"❌ LLM error: {e}")
            return f"I heard: {user_input}"
    
    async def speak_text(self, text):
        """Speak text using TTS"""
        try:
            # Try edge-tts first
            try:
                import edge_tts
                communicate = edge_tts.Communicate(text, "en-US-AriaNeural")
                
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                    await communicate.save(tmp_file.name)
                    self.play_audio(tmp_file.name)
                    os.unlink(tmp_file.name)
                return True
            except ImportError:
                pass
            
            # Try pyttsx3
            try:
                import pyttsx3
                engine = pyttsx3.init()
                engine.say(text)
                engine.runAndWait()
                return True
            except ImportError:
                pass
            
            # System fallback
            self.speak_system(text)
            return True
            
        except Exception as e:
            print(f"❌ TTS error: {e}")
            return False
    
    def speak_system(self, text):
        """System TTS fallback"""
        try:
            if os.name == 'nt':  # Windows
                import subprocess
                clean_text = text.replace("'", "''").replace('"', '""')
                cmd = f'Add-Type -AssemblyName System.Speech; $synth = New-Object System.Speech.Synthesis.SpeechSynthesizer; $synth.Speak("{clean_text}")'
                subprocess.run(['powershell', '-Command', cmd], capture_output=True, timeout=30)
        except Exception as e:
            print(f"❌ System TTS error: {e}")
    
    def play_audio(self, audio_file):
        """Play audio file"""
        try:
            if os.name == 'nt':  # Windows
                import subprocess
                subprocess.run(['powershell', '-c', f'(New-Object Media.SoundPlayer "{audio_file}").PlaySync()'], 
                             check=True, capture_output=True)
        except Exception as e:
            print(f"❌ Audio playback error: {e}")
    
    def record_audio_input(self):
        """Get audio input (simulated for now)"""
        print("🎤 Listening... (Press Enter when done speaking)")
        user_input = input("👤 Type what you want to say: ")
        return user_input
    
    async def conversation_loop(self):
        """Main conversation loop"""
        print("\n🎙️ VOICE CONVERSATION STARTED")
        print("=" * 40)
        print("💬 Start talking! (Type 'exit' to quit)")
        print("=" * 40)
        
        self.is_running = True
        
        try:
            while self.is_running:
                self.conversation_count += 1
                print(f"\n--- Conversation {self.conversation_count} ---")
                
                # Get user input (for now, typed input)
                user_input = self.record_audio_input()
                
                if not user_input or user_input.strip() == "":
                    print("❌ No input received")
                    continue
                
                # Check for exit
                if user_input.lower() in ['exit', 'quit', 'goodbye', 'stop']:
                    print("👋 Goodbye!")
                    await self.speak_text("Goodbye! It was nice talking with you.")
                    break
                
                print(f"👤 You: {user_input}")
                
                # Get AI response
                ai_response = self.get_ai_response(user_input)
                print(f"🤖 AI: {ai_response}")
                
                # Speak response
                await self.speak_text(ai_response)
                
                print("✅ Conversation cycle complete")
                
        except KeyboardInterrupt:
            print("\n👋 Conversation ended")
        except Exception as e:
            print(f"\n❌ Conversation error: {e}")
        finally:
            self.is_running = False
    
    async def start(self):
        """Start the voice chat system"""
        print("🚀 STARTING SIMPLE VOICE CHAT")
        print("=" * 50)
        
        # Check dependencies
        deps = self.check_dependencies()
        
        # Initialize components
        if not self.init_stt(deps):
            print("❌ STT initialization failed")
            return False
        
        if not self.init_llm(deps):
            print("❌ LLM initialization failed")
            return False
        
        print("✅ All components ready!")
        print("🎯 Voice chat system is ready for conversations")
        
        # Start conversation
        await self.conversation_loop()
        
        return True

async def main():
    """Main function"""
    chat = SimpleVoiceChat()
    await chat.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
