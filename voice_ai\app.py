#!/usr/bin/env python3
"""voice_ai.app

The core application logic for the voice AI assistant.
"""

import asyncio
import logging
import time
import tempfile
import os
import sys
from pathlib import Path

import numpy as np
from faster_whisper import WhisperModel

from voice_ai.config import settings
from voice_ai.audio import AsyncRecorder, play_audio
from voice_ai.utils import safe_call
from project.ollama_integration import create_ollama_integration, OllamaVoiceIntegration

# Module-level logger
logger = logging.getLogger(__name__)

class VoiceAI:
    """The main Voice AI application class."""

    def __init__(self):
        self.ollama_integration: OllamaVoiceIntegration | None = None
        self.stt_model: WhisperModel | None = None
        self.session_stats = {
            "queries_processed": 0,
            "total_stt_time": 0.0,
            "total_llm_time": 0.0,
            "total_tts_time": 0.0,
            "session_start": time.time(),
        }

    async def initialize(self) -> bool:
        """Initialize all components of the Voice AI."""
        logger.info("🚀 Initializing Voice AI...")

        # Initialize Ollama
        logger.info("🔧 Connecting to Ollama...")
        self.ollama_integration = await create_ollama_integration()
        if not self.ollama_integration:
            logger.error("❌ Failed to initialize Ollama integration")
            return False

        # Initialize STT
        try:
            logger.info("🔄 Loading faster-whisper model… (%s)", settings.stt.model_size)
            self.stt_model = WhisperModel(
                settings.stt.model_size,
                device="cpu",
                compute_type="int8"
            )
            logger.info("✅ STT initialized (faster-whisper)")
        except Exception as e:
            logger.error("⚠️ STT initialization failed: %s", e)
            return False

        logger.info("✅ Voice AI ready!")
        return True

    async def record_and_transcribe(self) -> str | None:
        """Record audio and transcribe it to text."""
        try:
            recorder = AsyncRecorder(
                sample_rate=settings.audio.sample_rate,
                chunk_duration=settings.audio.chunk_duration,
                vad_level=settings.audio.vad_level,
                max_silence=1.0,
            )
            audio_data = await recorder.record_until_silence()
            if audio_data is None or audio_data.size == 0:
                logger.info("🤷 No audio recorded.")
                return None

            logger.info("🎤 Recorded %.2fs of audio", len(audio_data) / settings.audio.sample_rate)

            # Transcribe
            start_time = time.time()
            segments, _ = self.stt_model.transcribe(
                audio_data, beam_size=settings.stt.beam_size, language="en"
            )
            transcription = " ".join([s.text for s in segments]).strip()
            stt_time = time.time() - start_time
            self.session_stats["total_stt_time"] += stt_time
            logger.info("🎯 Transcribed in %.2fs: %s", stt_time, transcription)
            return transcription

        except Exception as e:
            logger.error("❌ Recording/Transcription failed: %s", e)
            return None

    async def generate_and_speak(self, text: str):
        """Generate a response from the LLM and speak it."""
        if not self.ollama_integration:
            return

        logger.info("💭 Processing: %s", text)
        start_time = time.time()

        # Generate streaming response
        full_response = ""
        async for chunk in self.ollama_integration.generate_response(text, stream=True):
            full_response += chunk

        llm_time = time.time() - start_time
        self.session_stats["total_llm_time"] += llm_time
        self.session_stats["queries_processed"] += 1
        logger.info("🤖 Generated response in %.2fs", llm_time)

        # Speak the response
        await self.speak_text(full_response.strip())

    async def speak_text(self, text: str):
        """Synthesize text to speech and play it."""
        if not text:
            return

        try:
            import edge_tts

            start_time = time.time()
            communicate = edge_tts.Communicate(
                text,
                settings.tts.voice,
                rate=settings.tts.rate,
                volume=settings.tts.volume,
            )

            # Create a closed temporary file path so Edge-TTS can write freely (Windows friendly)
            fd, wav_path = tempfile.mkstemp(suffix=".wav")
            os.close(fd)  # Close the OS handle immediately

            await communicate.save(wav_path)
            await safe_call(play_audio, Path(wav_path), retries=2)
            # File will be cleaned up by the player

            tts_time = time.time() - start_time
            self.session_stats["total_tts_time"] += tts_time
            logger.info("🔊 Spoke in %.2fs", tts_time)

        except Exception as e:
            logger.error("❌ TTS error: %s", e)
            logger.info("🔊 [TTS failed] AI says: %s", text)

    async def get_status(self) -> dict:
        """Return a dictionary with the current system status and stats."""
        ollama_status = await self.ollama_integration.get_status() if self.ollama_integration else {}
        return {
            "session_stats": self.session_stats,
            "ollama_status": ollama_status,
        }

    async def shutdown(self):
        """Clean up resources."""
        logger.info("🔌 Shutting down...")
        if self.ollama_integration:
            await self.ollama_integration.shutdown()
        logger.info("👋 Goodbye!") 